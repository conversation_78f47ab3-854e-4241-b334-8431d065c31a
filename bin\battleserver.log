[BattleStateManager_436969438183434] ===== STARTING NEW ROUND 6 =====
[BattleStateManager_436969438183434] Round 6 has buff selection: True
[BattleStateManager_436969438183434] Publishing RoundStartedEvent for round 6
[AutoChessScene_436969438183434] Round 6 started
[BattleStateManager_436969438183434] Setting state to StateRoundStart for round 6
[BattleStateManager_436969438183434] State: StateRoundSettlement -> StateRoundStart (R6, 1000ms)
[AutoChessScene_436969438183434] HandleRoundStart: 2 active players
[AutoChessScene_436969438183434] Valid player: 10102021302, Health: 3
[AutoChessScene_436969438183434] Valid player: 90000032129, Health: 1
[AutoChessScene_436969438183434] Player 10102021302 has 20 entities to save
[PlayerManager_436969438183434] Saved board data: player:10102021302 entities:20
[PlayerManager_436969438183434] Saved prev round data: player:10102021302 entities:20
[AutoChessScene_436969438183434] Player 90000032129 has 25 entities to save
[PlayerManager_436969438183434] Saved board data: player:90000032129 entities:25
[PlayerManager_436969438183434] Saved prev round data: player:90000032129 entities:25
[OpponentPairManager] Generating opponent pairs for round 1, active players: 2, eliminated: 0
[OpponentPairManager] Random pair: Player 10102021302 vs Player 90000032129
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_436969438183434] Created 2 opponent pairs
[PlayerManager_436969438183434] Set player opponents, count: 2
[BattleInstanceManager] Created instance 436969438183434_1 for active players 10102021302 vs 90000032129
[CheckerBoard_436969438183434] Cleared checkerboard
[CheckerBoard_436969438183434] Initialized
[BattleInstance] 436969438183434_1 created with players: 10102021302, 90000032129
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_436969438183434] Restoring player 10102021302 to My area (rows 1-5) based on current instance position
[CheckerBoard_436969438183434] Placed entity 1 at position (1, 1)
[CheckerBoard_436969438183434] Created entity ID:1 ConfigID:101 StarLevel:1 at (1, 1) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 1: (1,1)->(1,1), GridID:1->1, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 2 at position (1, 2)
[CheckerBoard_436969438183434] Created entity ID:2 ConfigID:103 StarLevel:1 at (1, 2) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 2: (1,2)->(1,2), GridID:2->2, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 3 at position (1, 3)
[CheckerBoard_436969438183434] Created entity ID:3 ConfigID:102 StarLevel:1 at (1, 3) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 3: (1,3)->(1,3), GridID:3->3, ConfigID:102, StarLevel:3
[CheckerBoard_436969438183434] Placed entity 4 at position (1, 4)
[CheckerBoard_436969438183434] Created entity ID:4 ConfigID:103 StarLevel:1 at (1, 4) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 4: (1,5)->(1,4), GridID:5->4, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 5 at position (1, 5)
[CheckerBoard_436969438183434] Created entity ID:5 ConfigID:102 StarLevel:1 at (1, 5) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 5: (1,6)->(1,5), GridID:6->5, ConfigID:102, StarLevel:2
[CheckerBoard_436969438183434] Placed entity 6 at position (1, 6)
[CheckerBoard_436969438183434] Created entity ID:6 ConfigID:101 StarLevel:1 at (1, 6) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 6: (2,1)->(1,6), GridID:7->6, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 7 at position (2, 1)
[CheckerBoard_436969438183434] Created entity ID:7 ConfigID:103 StarLevel:1 at (2, 1) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 7: (2,2)->(2,1), GridID:8->7, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 8 at position (2, 2)
[CheckerBoard_436969438183434] Created entity ID:8 ConfigID:103 StarLevel:1 at (2, 2) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 8: (2,3)->(2,2), GridID:9->8, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 9 at position (2, 3)
[CheckerBoard_436969438183434] Created entity ID:9 ConfigID:102 StarLevel:1 at (2, 3) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 9: (2,4)->(2,3), GridID:10->9, ConfigID:102, StarLevel:2
[CheckerBoard_436969438183434] Placed entity 10 at position (2, 4)
[CheckerBoard_436969438183434] Created entity ID:10 ConfigID:101 StarLevel:1 at (2, 4) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 10: (2,5)->(2,4), GridID:11->10, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 11 at position (2, 5)
[CheckerBoard_436969438183434] Created entity ID:11 ConfigID:103 StarLevel:1 at (2, 5) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 11: (2,6)->(2,5), GridID:12->11, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 12 at position (2, 6)
[CheckerBoard_436969438183434] Created entity ID:12 ConfigID:103 StarLevel:1 at (2, 6) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 12: (3,1)->(2,6), GridID:13->12, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 13 at position (3, 1)
[CheckerBoard_436969438183434] Created entity ID:13 ConfigID:103 StarLevel:1 at (3, 1) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 13: (3,2)->(3,1), GridID:14->13, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 14 at position (3, 2)
[CheckerBoard_436969438183434] Created entity ID:14 ConfigID:101 StarLevel:1 at (3, 2) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 14: (3,3)->(3,2), GridID:15->14, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 15 at position (3, 3)
[CheckerBoard_436969438183434] Created entity ID:15 ConfigID:101 StarLevel:1 at (3, 3) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 15: (3,5)->(3,3), GridID:17->15, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 16 at position (3, 4)
[CheckerBoard_436969438183434] Created entity ID:16 ConfigID:101 StarLevel:1 at (3, 4) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 16: (3,6)->(3,4), GridID:18->16, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 17 at position (3, 5)
[CheckerBoard_436969438183434] Created entity ID:17 ConfigID:101 StarLevel:1 at (3, 5) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 17: (4,1)->(3,5), GridID:19->17, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 18 at position (3, 6)
[CheckerBoard_436969438183434] Created entity ID:18 ConfigID:102 StarLevel:1 at (3, 6) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 18: (5,1)->(3,6), GridID:25->18, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 19 at position (4, 1)
[CheckerBoard_436969438183434] Created entity ID:19 ConfigID:102 StarLevel:1 at (4, 1) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 19: (5,2)->(4,1), GridID:26->19, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 20 at position (4, 2)
[CheckerBoard_436969438183434] Created entity ID:20 ConfigID:102 StarLevel:1 at (4, 2) for player 10102021302
[AutoChessScene_436969438183434] Restored entity 20: (5,6)->(4,2), GridID:30->20, ConfigID:102, StarLevel:1
[AutoChessScene_436969438183434] Restored board: player:10102021302 entities:20/20
[AutoChessScene_436969438183434] Restoring player 90000032129 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_436969438183434] Placed entity 21 at position (6, 1)
[CheckerBoard_436969438183434] Created entity ID:21 ConfigID:101 StarLevel:1 at (6, 1) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 21: (6,1)->(6,1), GridID:31->31, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 22 at position (6, 2)
[CheckerBoard_436969438183434] Created entity ID:22 ConfigID:102 StarLevel:1 at (6, 2) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 22: (6,2)->(6,2), GridID:32->32, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 23 at position (6, 3)
[CheckerBoard_436969438183434] Created entity ID:23 ConfigID:101 StarLevel:1 at (6, 3) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 23: (6,3)->(6,3), GridID:33->33, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 24 at position (6, 4)
[CheckerBoard_436969438183434] Created entity ID:24 ConfigID:103 StarLevel:1 at (6, 4) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 24: (6,4)->(6,4), GridID:34->34, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 25 at position (6, 5)
[CheckerBoard_436969438183434] Created entity ID:25 ConfigID:101 StarLevel:1 at (6, 5) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 25: (6,5)->(6,5), GridID:35->35, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 26 at position (6, 6)
[CheckerBoard_436969438183434] Created entity ID:26 ConfigID:103 StarLevel:1 at (6, 6) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 26: (6,6)->(6,6), GridID:36->36, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 27 at position (7, 1)
[CheckerBoard_436969438183434] Created entity ID:27 ConfigID:101 StarLevel:1 at (7, 1) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 27: (7,1)->(7,1), GridID:37->37, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 28 at position (7, 2)
[CheckerBoard_436969438183434] Created entity ID:28 ConfigID:102 StarLevel:1 at (7, 2) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 28: (7,2)->(7,2), GridID:38->38, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 29 at position (7, 3)
[CheckerBoard_436969438183434] Created entity ID:29 ConfigID:102 StarLevel:1 at (7, 3) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 29: (7,3)->(7,3), GridID:39->39, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 30 at position (7, 4)
[CheckerBoard_436969438183434] Created entity ID:30 ConfigID:102 StarLevel:1 at (7, 4) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 30: (7,4)->(7,4), GridID:40->40, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 31 at position (7, 5)
[CheckerBoard_436969438183434] Created entity ID:31 ConfigID:101 StarLevel:1 at (7, 5) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 31: (7,5)->(7,5), GridID:41->41, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 32 at position (7, 6)
[CheckerBoard_436969438183434] Created entity ID:32 ConfigID:101 StarLevel:1 at (7, 6) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 32: (7,6)->(7,6), GridID:42->42, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 33 at position (8, 1)
[CheckerBoard_436969438183434] Created entity ID:33 ConfigID:102 StarLevel:1 at (8, 1) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 33: (8,1)->(8,1), GridID:43->43, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 34 at position (8, 2)
[CheckerBoard_436969438183434] Created entity ID:34 ConfigID:103 StarLevel:1 at (8, 2) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 34: (8,2)->(8,2), GridID:44->44, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 35 at position (8, 3)
[CheckerBoard_436969438183434] Created entity ID:35 ConfigID:103 StarLevel:1 at (8, 3) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 35: (8,3)->(8,3), GridID:45->45, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 36 at position (8, 4)
[CheckerBoard_436969438183434] Created entity ID:36 ConfigID:101 StarLevel:1 at (8, 4) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 36: (8,4)->(8,4), GridID:46->46, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 37 at position (8, 5)
[CheckerBoard_436969438183434] Created entity ID:37 ConfigID:103 StarLevel:1 at (8, 5) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 37: (8,5)->(8,5), GridID:47->47, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 38 at position (8, 6)
[CheckerBoard_436969438183434] Created entity ID:38 ConfigID:102 StarLevel:1 at (8, 6) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 38: (8,6)->(8,6), GridID:48->48, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 39 at position (9, 1)
[CheckerBoard_436969438183434] Created entity ID:39 ConfigID:103 StarLevel:1 at (9, 1) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 39: (9,1)->(9,1), GridID:49->49, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 40 at position (9, 2)
[CheckerBoard_436969438183434] Created entity ID:40 ConfigID:103 StarLevel:1 at (9, 2) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 40: (9,2)->(9,2), GridID:50->50, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 41 at position (9, 3)
[CheckerBoard_436969438183434] Created entity ID:41 ConfigID:102 StarLevel:1 at (9, 3) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 41: (9,3)->(9,3), GridID:51->51, ConfigID:102, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 42 at position (9, 4)
[CheckerBoard_436969438183434] Created entity ID:42 ConfigID:101 StarLevel:1 at (9, 4) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 42: (9,4)->(9,4), GridID:52->52, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 43 at position (9, 5)
[CheckerBoard_436969438183434] Created entity ID:43 ConfigID:103 StarLevel:1 at (9, 5) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 43: (10,2)->(9,5), GridID:56->53, ConfigID:103, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 44 at position (9, 6)
[CheckerBoard_436969438183434] Created entity ID:44 ConfigID:101 StarLevel:1 at (9, 6) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 44: (10,5)->(9,6), GridID:59->54, ConfigID:101, StarLevel:1
[CheckerBoard_436969438183434] Placed entity 45 at position (10, 1)
[CheckerBoard_436969438183434] Created entity ID:45 ConfigID:102 StarLevel:1 at (10, 1) for player 90000032129
[AutoChessScene_436969438183434] Restored entity 45: (10,6)->(10,1), GridID:60->55, ConfigID:102, StarLevel:1
[AutoChessScene_436969438183434] Restored board: player:90000032129 entities:25/25
[AutoChessScene_436969438183434] Cleaned orphaned entities for player 10102021302
[AutoChessScene_436969438183434] Cleaned orphaned entities for player 90000032129
[PlayerManager_436969438183434] Reset all players ready status
[AutoChessScene_436969438183434] Round started with 1 battle instances
[AutoChessScene_436969438183434] Generating buff options for all players
[BuffManager_436969438183434] Generated 3 buff options for player 10102021302: [110, 104, 107]
[AutoChessScene_436969438183434] Generated 3 buff options for player 10102021302: [110, 104, 107]
[BuffManager_436969438183434] Generated 3 buff options for player 90000032129: [110, 109, 103]
[AutoChessScene_436969438183434] Generated 3 buff options for player 90000032129: [110, 109, 103]
[AutoChessScene_436969438183434] Player status: Total=4, Active=2
[AutoChessScene_436969438183434] Player 10102021302: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436969438183434] Player 90000092467: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_436969438183434] Player 90000032129: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_436969438183434] Player 90000073438: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_436969438183434] Sending RoundStart notifications to 2 active players...
[AutoChessScene_436969438183434] RoundStart: Player 10102021302 buff options: [110, 104, 107]
[AutoChessScene_436969438183434] RoundStart board data: player:10102021302 heroes:20
[AutoChessScene_436969438183434] RoundStart board data: player:90000032129 heroes:25
[AutoChessScene_436969438183434] Sending RoundStart to Player 10102021302 on GameServer 10102
[AutoChessScene_436969438183434] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_436969438183434] RoundStart: Player 90000032129 buff options: [110, 109, 103]
[AutoChessScene_436969438183434] RoundStart board data: player:10102021302 heroes:20
[AutoChessScene_436969438183434] RoundStart board data: player:90000032129 heroes:25
[AutoChessScene_436969438183434] Sending RoundStart to Player 90000032129 on GameServer 10102
[AutoChessScene_436969438183434] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_436969438183434] Successfully sent RoundStart notifications to all 2 players via NATS
[BattleService] Updated battle 436969438183434 state to StateRoundStart
[AutoChessScene_436969438183434] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R6)
[BattleStateManager_436969438183434] BattleStateChangedEvent published successfully
[BattleStateManager_436969438183434] ===== ROUND 6 INITIALIZATION COMPLETE =====
[BattleStateManager_436969438183434] Buff selection timer started: 25000ms
[BattleStateManager_436969438183434] State: StateRoundStart -> StatePreparation (R6, 65000ms)
[AutoChessScene_436969438183434] Preparation phase started
[AutoChessScene_436969438183434] No buff options available for bot player 90000092467
[BuffManager_436969438183434] Added buff 110 (Buff_110) to player 90000032129
[AutoChessScene_436969438183434] Auto-selected buff 110 for bot player 90000032129
[CheckerBoard_436969438183434] Placed entity 46 at position (10, 5)
[CheckerBoard_436969438183434] Created entity ID:46 ConfigID:102 StarLevel:1 at (10, 5) for player 90000032129
[CheckerBoard_436969438183434] Placed entity 47 at position (10, 4)
[CheckerBoard_436969438183434] Created entity ID:47 ConfigID:103 StarLevel:1 at (10, 4) for player 90000032129
[CheckerBoard_436969438183434] Placed entity 48 at position (10, 2)
[CheckerBoard_436969438183434] Created entity ID:48 ConfigID:102 StarLevel:1 at (10, 2) for player 90000032129
[CheckerBoard_436969438183434] Placed entity 49 at position (10, 6)
[CheckerBoard_436969438183434] Created entity ID:49 ConfigID:103 StarLevel:1 at (10, 6) for player 90000032129
[CheckerBoard_436969438183434] Placed entity 50 at position (10, 3)
[CheckerBoard_436969438183434] Created entity ID:50 ConfigID:101 StarLevel:1 at (10, 3) for player 90000032129
[CheckerBoard_436969438183434] Generated 5/5 heroes for player 90000032129: 5 placed, 0 in temporary slots
[CheckerBoard_436969438183434] Generated 5 heroes for player 90000032129 in Enemy area
[AutoChessScene_436969438183434] Generated 5 heroes for player 90000032129: 5 placed on board, 0 in temporary slots
[AutoChessScene_436969438183434] Generated 5 new heroes for bot player 90000032129 after buff selection
[AutoChessScene_436969438183434] No buff options available for bot player 90000073438
[PlayerManager_436969438183434] Player 90000032129 ready status set to True
[AutoChessScene_436969438183434] Auto-ready 1 additional bots
[AutoChessScene_436969438183434] Free operation phase started
[BattleService] Updated battle 436969438183434 state to StatePreparation
[AutoChessScene_436969438183434] State change sent to GameServer: StateRoundStart -> StatePreparation (R6)
[BattleStateManager_436969438183434] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10102021302 is selecting buff 104
[BuffManager_436969438183434] Added buff 104 (Buff_104) to player 10102021302
[CheckerBoard_436969438183434] Placed entity 51 at position (4, 4)
[CheckerBoard_436969438183434] Created entity ID:51 ConfigID:101 StarLevel:1 at (4, 4) for player 10102021302
[CheckerBoard_436969438183434] Placed entity 52 at position (5, 3)
[CheckerBoard_436969438183434] Created entity ID:52 ConfigID:102 StarLevel:1 at (5, 3) for player 10102021302
[CheckerBoard_436969438183434] Placed entity 53 at position (4, 6)
[CheckerBoard_436969438183434] Created entity ID:53 ConfigID:103 StarLevel:1 at (4, 6) for player 10102021302
[CheckerBoard_436969438183434] Placed entity 54 at position (4, 5)
[CheckerBoard_436969438183434] Created entity ID:54 ConfigID:103 StarLevel:1 at (4, 5) for player 10102021302
[CheckerBoard_436969438183434] Placed entity 55 at position (5, 1)
[CheckerBoard_436969438183434] Created entity ID:55 ConfigID:102 StarLevel:1 at (5, 1) for player 10102021302
[CheckerBoard_436969438183434] Generated 5/5 heroes for player 10102021302: 5 placed, 0 in temporary slots
[CheckerBoard_436969438183434] Generated 5 heroes for player 10102021302 in My area
[AutoChessScene_436969438183434] Generated 5 heroes for player 10102021302: 5 placed on board, 0 in temporary slots
[AutoChessScene_436969438183434] Player 10102021302 selected buff 104, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[AutoChessScene_436969438183434] MergeHero operation: Player 10102021302, From GridID 4 → To GridID 11
[AutoChessScene_436969438183434] MergeHero with no move operations
[BattleInstance] 436969438183434_1 MERGE VALIDATION: After processing 0 moves
[BattleInstance] 436969438183434_1 - Source GridID 4: EntityId=4, OwnerId=10102021302
[BattleInstance] 436969438183434_1 - Target GridID 11: EntityId=11, OwnerId=10102021302
[CheckerBoard_436969438183434] Merged entity from (1, 4) to (2, 5). Star level increased from 1 to 2
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_436969438183434] Processing 1 move operations before setting player 10102021302 ready
[CheckerBoard_436969438183434] MoveEntity called: (2,5) → (2,6)
[CheckerBoard_436969438183434] Move entities: From EntityId=11, To EntityId=12
[CheckerBoard_436969438183434] Target position occupied, executing swap: Entity 11 ? Entity 12
[CheckerBoard_436969438183434] SwapEntities called: (2,5) ? (2,6)
[CheckerBoard_436969438183434] Swap entities: Position1 EntityId=11, Position2 EntityId=12
[CheckerBoard_436969438183434] Updated Entity 12 position to (2,5)
[CheckerBoard_436969438183434] Updated Entity 11 position to (2,6)
[CheckerBoard_436969438183434] Swap completed successfully: (2,5) ? (2,6)
[CheckerBoard_436969438183434] Swap operation result: True
[AutoChessScene_436969438183434] Player 10102021302 move operation: GridID(11->12) → Coord((2,5)->(2,6)), success: True
[PlayerManager_436969438183434] Player 10102021302 ready status set to True
[PlayerManager_436969438183434] All players are ready!
[AutoChessScene_436969438183434] All players are ready, transitioning to next state
[BattleStateManager_436969438183434] State: StatePreparation -> StateBattleStarting (R6, 1000ms)
[AutoChessScene_436969438183434] Applying battle start buffs for all players
[BuffManager_436969438183434] Applying battle start buff 109 (Buff_109) for player 10102021302
[BuffManager_436969438183434] Applying battle start buff 101 (Buff_101) for player 10102021302
[BuffManager_436969438183434] Applying battle start buff 104 (Buff_104) for player 10102021302
[BuffManager_436969438183434] Applying battle start buff 101 (Buff_101) for player 90000032129
[BuffManager_436969438183434] Applying battle start buff 108 (Buff_108) for player 90000032129
[BuffManager_436969438183434] Applying battle start buff 110 (Buff_110) for player 90000032129
[AutoChessScene_436969438183434] Camp info for player 10102021302: 24 heroes added
[AutoChessScene_436969438183434] Camp info for player 90000032129: 30 heroes added
[AutoChessScene_436969438183434] Created RoundBattleStart request for player 10102021302, Team order: [10102021302, 90000032129], total GridIDs used: 54
[AutoChessScene_436969438183434] Sent RoundBattleStart to Player 10102021302 vs Opponent 90000032129 with 2 teams
[AutoChessScene_436969438183434] Camp info for player 10102021302: 24 heroes added
[AutoChessScene_436969438183434] Camp info for player 90000032129: 30 heroes added
[AutoChessScene_436969438183434] Created RoundBattleStart request for player 90000032129, Team order: [10102021302, 90000032129], total GridIDs used: 54
[AutoChessScene_436969438183434] Sent RoundBattleStart to Player 90000032129 vs Opponent 10102021302 with 2 teams
[AutoChessScene_436969438183434] Sent RoundBattleStart notifications with seed: 924931594
[BattleService] Updated battle 436969438183434 state to StateBattleStarting
[AutoChessScene_436969438183434] State change sent to GameServer: StatePreparation -> StateBattleStarting (R6)
[BattleStateManager_436969438183434] BattleStateChangedEvent published successfully
[AutoChessScene_436969438183434] Player 10102021302 set ready status to True
[BattleStateManager_436969438183434] State: StateBattleStarting -> StateBattleInProgress (R6, 65000ms)
[AutoChessScene_436969438183434] Starting all battle instances
[BattleInstance] 436969438183434_1 battle started
[BattleInstanceManager] Started all 1 battle instances
[AutoChessScene_436969438183434] Bot 90000032129 vs real player 10102021302, waiting for real player result
[AutoChessScene_436969438183434] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 436969438183434 state to StateBattleInProgress
[AutoChessScene_436969438183434] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R6)
[BattleStateManager_436969438183434] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10102021302, win: True
[AutoChessScene_436969438183434] Player 10102021302 sent EndBattleReq (win: True), instance: 436969438183434_1
[AutoChessScene_436969438183434] Auto EndBattle for bot 90000032129 vs real player 10102021302, bot result: False
[BattleInstance] 436969438183434_1 battle finished, winner: 10102021302, loser: 90000032129
[AutoChessScene_436969438183434] Battle instance 436969438183434_1 completed: Winner 10102021302, Loser 90000032129
[AutoChessScene_436969438183434] All battle instances finished, proceeding to settlement
[BattleStateManager_436969438183434] State: StateBattleInProgress -> StateRoundSettlement (R6, 5000ms)
[AutoChessScene_436969438183434] Processing battle results
[PlayerManager_436969438183434] Player 90000032129 health reduced by 1, current health: 0
[PlayerManager_436969438183434] Player 90000032129 eliminated from battle 436969438183434
[AutoChessScene_436969438183434] Player 90000032129 has been eliminated
[AutoChessScene_436969438183434] Cleared 0 entities for player 90000032129
[PlayerManager_436969438183434] Game over! Winner: Player 10102021302
[AutoChessScene_436969438183434] Game over! Winner: Player 10102021302
[BattleStateManager_436969438183434] State: StateRoundSettlement -> StateGameOver (R6, 0ms)
[AutoChessScene_436969438183434] Game over processing
[AutoChessScene_436969438183434] Game winner: Player 10102021302
[AutoChessScene_436969438183434] Notifying GameServer: Game over, winner: 10102021302
[AutoChessScene_436969438183434] Player 10102021302 final lineup: 54 heroes
[AutoChessScene_436969438183434] Player 90000092467 final lineup: 0 heroes
[AutoChessScene_436969438183434] Player 90000032129 final lineup: 54 heroes
[AutoChessScene_436969438183434] Player 90000073438 final lineup: 0 heroes
[AutoChessScene_436969438183434] Sent BattleEnd notifications for 4 players
[AutoChessScene_436969438183434] Game over processing completed, waiting for cleanup signal
[BattleService] Updated battle 436969438183434 state to StateGameOver
[AutoChessScene_436969438183434] State change sent to GameServer: StateRoundSettlement -> StateGameOver (R6)
[BattleStateManager_436969438183434] BattleStateChangedEvent published successfully
[AutoChessScene_436969438183434] Scheduling delayed cleanup in 5 seconds
[AutoChessScene_436969438183434] Player 90000032129 lost 1 health, winner: 10102021302
[AutoChessScene_436969438183434] Sent FINAL RoundBattleEnd notification (isEnd=true) - Winner: 10102021302, Loser: 90000032129
[BattleStateManager_436969438183434] Invalid state transition from StateGameOver to StateRoundSettlement
[AutoChessScene_436969438183434] Checking players elimination
[AutoChessScene_436969438183434] Active players remaining: 1
[BattleService] Updated battle 436969438183434 state to StateRoundSettlement
[AutoChessScene_436969438183434] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R6)
[BattleStateManager_436969438183434] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_436969438183434] Executing delayed cleanup
[BattleService] Starting cleanup for battle 436969438183434
[BattleService] Removed battle state for 436969438183434
[CheckerBoard_436969438183434] Cleared all entities
[BuffManager_436969438183434] Cleared all buffs
[AutoChessScene_436969438183434] Scene resources disposed
[SceneManager] Removed AutoChessScene 436969438183434 from thread management
[BattleService] Cleaned up scene for battle 436969438183434
[BattleService] Cleanup completed for battle 436969438183434
[BattleService] Player 10102021302 (勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000046504 (AI_勤奋的.矿工) Level 0 Tro