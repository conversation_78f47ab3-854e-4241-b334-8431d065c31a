using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LiteFrame.Framework;
using BattleServer.Server;
using BattleServer.Game.Board;
using BattleServer.Game.Battle;
using BattleServer.Game.Player;
using BattleServer.Game.Core;
using BattleServer.Nats;
using BattleServer.Service;
using Game.Core;
using BattleState = Game.Core.BattleState;

namespace BattleServer.Game.AutoChess
{
    /// <summary>
    /// 自走棋战斗场景 - 优化版
    /// 使用事件驱动的组件化设计，提供更好的扩展性和可测试性
    /// </summary>
    public class AutoChessScene : Scene, IDisposable
    {
        public long BattleId { get; private set; }

        // 组件管理器
        private readonly BattleStateManager _battleStateManager;
        private readonly PlayerManager _playerManager;
        private readonly BattleInstanceManager _instanceManager;
        private readonly OpponentPairManager _opponentPairManager;
        private readonly BuffManager _buffManager;
        private readonly BattleEventBus _eventBus;
        private readonly CheckerBoard _checkerBoard;

        // 目标 GameServer ID（用于 NATS 消息路由）
        private readonly string _gameServerId;
        private string _logName;

        // 游戏结束标志，防止重复发送BattleEnd
        private bool _battleEndSent = false;

        /// <summary>
        /// 构造函数 - 使用依赖注入模式
        /// </summary>
        public AutoChessScene(string gameServerId = "10106", BattleEventBus? eventBus = null)
        {
            _gameServerId = gameServerId;
            _eventBus = eventBus ?? new BattleEventBus();

            // 创建组件，共享事件总线
            _battleStateManager = new BattleStateManager(_eventBus);
            _playerManager = new PlayerManager(_eventBus);
            _instanceManager = new BattleInstanceManager();
            _opponentPairManager = new OpponentPairManager();
            _buffManager = new BuffManager();
            _checkerBoard = new CheckerBoard(_eventBus);

            _logName = "AutoChessScene";
        }

        /// <summary>
        /// 初始化战斗
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="playerIds">玩家ID列表</param>
        /// <param name="playerLineups">玩家队伍数据</param>
        /// <param name="playerHeroInfos">玩家完整英雄信息</param>
        /// <param name="playerServerIds">玩家服务器ID数据</param>
        public void InitBattle(long battleId, List<long> playerIds, Dictionary<long, List<int>> playerLineups, Dictionary<long, List<PBBattleHeroInfo>> playerHeroInfos = null, Dictionary<long, string> playerServerIds = null)
        {
            InitBattleWithoutStart(battleId, playerIds, playerLineups, playerHeroInfos, playerServerIds, null);

            // 立即开始第一回合（保持原有行为）
            _battleStateManager.StartNewRound();
        }

        /// <summary>
        /// 初始化战斗但不启动状态机 - 等待所有玩家进入后再启动
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="playerIds">玩家ID列表</param>
        /// <param name="playerLineups">玩家队伍数据</param>
        /// <param name="playerHeroInfos">玩家完整英雄信息</param>
        /// <param name="playerServerIds">玩家服务器ID数据</param>
        /// <param name="playerBasicInfos">玩家基本信息（名称、等级、奖杯）</param>
        public void InitBattleWithoutStart(long battleId, List<long> playerIds, Dictionary<long, List<int>> playerLineups, Dictionary<long, List<PBBattleHeroInfo>> playerHeroInfos = null, Dictionary<long, string> playerServerIds = null, Dictionary<long, (string name, int level, int trophy)> playerBasicInfos = null)
        {
            BattleId = battleId;
            _logName = $"AutoChessScene_{battleId}";

            try
            {
                // 初始化各个管理器组件
                _battleStateManager.Initialize(battleId);
                _playerManager.Initialize(battleId);
                _playerManager.Initialize(playerIds, playerLineups, playerHeroInfos, playerServerIds, playerBasicInfos);
                _instanceManager.Initialize(battleId);
                _instanceManager.SetPlayerManager(_playerManager); // 设置玩家管理器引用
                _opponentPairManager.Initialize(battleId);
                _buffManager.Initialize(battleId);
                _checkerBoard.Initialize(battleId);

                // 注册事件处理
                RegisterEventHandlers();

                Log.Info($"[{_logName}] Battle {battleId} initialized with {playerIds.Count} players, waiting for all players to enter");
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Failed to initialize battle: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 启动战斗状态机 - 当所有玩家都进入后调用
        /// </summary>
        public void StartBattleStateMachine()
        {
            try
            {
                Log.Info($"[{_logName}] StartBattleStateMachine() called for battle {BattleId}");

                if (_battleStateManager == null)
                {
                    Log.Error($"[{_logName}] Cannot start battle state machine: BattleStateManager not initialized");
                    return;
                }

                Log.Info($"[{_logName}] BattleStateManager is ready, starting first round...");
                Log.Info($"[{_logName}] Current state before starting: {_battleStateManager.CurrentState}");

                // 开始第一回合
                _battleStateManager.StartNewRound();

                Log.Info($"[{_logName}] Battle state machine started successfully for battle {BattleId}");
                Log.Info($"[{_logName}] Current state after starting: {_battleStateManager.CurrentState}");
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Failed to start battle state machine: {ex.Message}");
                Log.Error($"[{_logName}] Exception stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 注册事件处理器 - 使用事件总线模式
        /// </summary>
        private void RegisterEventHandlers()
        {
            // 注册战斗状态事件
            _eventBus.Subscribe<BattleStateChangedEvent>(OnBattleStateChanged);
            _eventBus.Subscribe<RoundStartedEvent>(OnRoundStarted);
            _eventBus.Subscribe<BattleTimeoutEvent>(OnBattleTimeout);
            _eventBus.Subscribe<BuffSelectionTimeoutEvent>(OnBuffSelectionTimeout);

            // 注册玩家事件
            _eventBus.Subscribe<AllPlayersReadyEvent>(OnAllPlayersReady);
            _eventBus.Subscribe<PlayerEliminatedEvent>(OnPlayerEliminated);
            _eventBus.Subscribe<GameOverEvent>(OnGameOver);

            // 注册实体事件
            _eventBus.Subscribe<EntityCreatedEvent>(OnEntityCreated);
            _eventBus.Subscribe<EntityMovedEvent>(OnEntityMoved);
            _eventBus.Subscribe<EntityMergedEvent>(OnEntityMerged);
            _eventBus.Subscribe<EntityRemovedEvent>(OnEntityRemoved);

            Log.Info($"[{_logName}] Event handlers registered");
        }

        /// <summary>
        /// 取消事件注册
        /// </summary>
        private void UnregisterEventHandlers()
        {
            _eventBus?.Clear();
        }

        /// <summary>
        /// 战斗状态变更事件处理
        /// </summary>
        private void OnBattleStateChanged(BattleStateChangedEvent evt)
        {
            // 移除重复的状态转换日志，BattleStateManager已经记录了

            // 根据新状态执行相应操作
            switch (evt.NewState)
            {
                case BattleState.StateRoundStart:
                    HandleRoundStart();

                    // 如果当前回合有Buff选择，先生成Buff选项
                    if (_battleStateManager.HasBuffSelectionThisRound)
                    {
                        GenerateBuffOptionsForAllPlayers();
                    }
                    else
                    {
                        GenerateHeroesForAllPlayers();
                    }

                    NotifyGameServerRoundStart();
                    break;
                case BattleState.StatePreparation:
                    HandlePreparationPhase();
                    break;
                case BattleState.StateBattleStarting:
                    ApplyBuffsForAllPlayers();
                    NotifyGameServerRoundBattleStart();
                    break;
                case BattleState.StateBattleInProgress:
                    StartAllBattleInstances();
                    AutoHandleBotBattles();
                    break;
                case BattleState.StateRoundSettlement:
                    ProcessBattleResults();
                    CheckPlayersElimination(); // 合并了原来StateEliminationCheck的逻辑
                    break;
                case BattleState.StateGameOver:
                    HandleGameOver();
                    break;
                default:
                    Log.Warning($"[{_logName}] Unhandled state: {evt.NewState}");
                    break;
            }

            // 同步状态到BattleService
            BattleServer.Service.BattleService.UpdateBattleState(BattleId, evt.NewState);

            // 通知GameServer状态已变更
            NotifyGameServerOnStateChanged(evt.OldState, evt.NewState, evt.CountdownMs);
        }

        /// <summary>
        /// 处理战斗超时
        /// </summary>
        private void OnBattleTimeout(BattleTimeoutEvent evt)
        {
            Log.Info($"[{_logName}] Battle timeout occurred in state {evt.State}");

            switch (evt.State)
            {
                case BattleState.StatePreparation:
                    // 准备阶段超时，强制所有玩家准备完毕
                    Log.Info($"[{_logName}] Preparation timeout - force ready all unready players");
                    ForceAllPlayersReady();
                    // 检查是否可以立即开始战斗
                    CheckAllPlayersReady();
                    break;

                case BattleState.StateBattleInProgress:
                    // 战斗超时，强制结束战斗
                    Log.Info($"[{_logName}] Battle timeout - force ending all unfinished battles");
                    ForceEndAllBattleInstances();
                    _battleStateManager.SetState(BattleState.StateRoundSettlement);
                    break;

                case BattleState.StateRoundSettlement:
                    // 结算确认超时，强制所有玩家确认（合并了原来的StateEliminationCheck逻辑）
                    Log.Info($"[{_logName}] Round settlement timeout - force confirm all players");
                    ForceAllPlayersConfirmRound();
                    break;
            }
        }

        /// <summary>
        /// 处理Buff选择超时
        /// </summary>
        private void OnBuffSelectionTimeout(BuffSelectionTimeoutEvent evt)
        {
            Log.Info($"[{_logName}] Buff selection timeout occurred - forcing buff selection for all unselected players");

            // 强制所有未选择buff的玩家随机选择
            ForceBuffSelectionForAllPlayers();
        }

        /// <summary>
        /// 强制所有未选择buff的玩家随机选择buff
        /// </summary>
        private void ForceBuffSelectionForAllPlayers()
        {
            var playerIds = _playerManager.GetAllPlayerIds();

            foreach (var playerId in playerIds)
            {
                // 检查玩家是否还有未选择的buff选项
                var buffOptions = _buffManager.GetPlayerBuffOptions(playerId);
                if (buffOptions.Count > 0)
                {
                    // 强制随机选择一个buff
                    bool success = _buffManager.PlayerRandomSelectBuff(playerId);
                    if (success)
                    {
                        Log.Info($"[{_logName}] Force random buff selection for player {playerId} due to buff timeout");

                        // 为选择buff的玩家生成新英雄
                        var newHeroes = GenerateHeroesForPlayer(playerId);
                        Log.Info($"[{_logName}] Generated {newHeroes.Count} new heroes for player {playerId} after forced buff selection");
                    }
                    else
                    {
                        Log.Warning($"[{_logName}] Failed to force buff selection for player {playerId}");
                    }
                }
            }
        }

        /// <summary>
        /// 强制结束所有战斗实例（超时处理）
        /// </summary>
        private void ForceEndAllBattleInstances()
        {
            var unfinishedInstances = _instanceManager.GetUnfinishedInstances();

            foreach (var instance in unfinishedInstances)
            {
                var playerIds = instance.PlayerIds;
                if (playerIds.Count >= 2)
                {
                    var player1Id = playerIds[0];
                    var player2Id = playerIds[1];

                    // 超时强制结算：根据减员数量或随机判定
                    long winnerId;
                    var allPlayerIds = _playerManager.GetAllPlayerIds();
                    var realPlayerId = allPlayerIds.FirstOrDefault(id => !id.ToString().StartsWith("9"));

                    if (player1Id == realPlayerId || player2Id == realPlayerId)
                    {
                        // 有真实玩家参与的战斗超时，按策划文档：根据减员数量判定
                        // 简化实现：随机判定（实际应该根据棋盘上剩余单位数量）
                        winnerId = playerIds[new Random().Next(2)];
                        Log.Info($"[{_logName}] Force timeout: Real player vs bot battle, random result: winner {winnerId}");
                    }
                    else
                    {
                        // 两个机器人对战，随机选择获胜者
                        winnerId = playerIds[new Random().Next(2)];
                        Log.Info($"[{_logName}] Force timeout: Bot vs bot battle, random winner: {winnerId}");
                    }

                    // 为两名玩家都调用HandleBattleEnd，确保触发正确的流程
                    var loserId = (winnerId == player1Id) ? player2Id : player1Id;

                    Log.Info($"[{_logName}] Force ending battle instance {instance.InstanceId} due to timeout");
                    var (_, _) = HandleBattleEnd((ulong)winnerId, true);   // 获胜者
                    var (_, _) = HandleBattleEnd((ulong)loserId, false);   // 失败者
                }
            }
        }

        /// <summary>
        /// 回合开始事件处理
        /// </summary>
        private void OnRoundStarted(RoundStartedEvent evt)
        {
            Log.Info($"[{_logName}] Round {evt.RoundCount} started");
            // HandleRoundStart() 已在 OnBattleStateChanged 的 StateRoundStart 分支中处理，避免重复调用
        }

        /// <summary>
        /// 处理回合开始逻辑
        /// </summary>
        private void HandleRoundStart()
        {
            // 0. 清理上一回合的跟踪数据
            _instanceEndBattleRequests.Clear();
            _roundConfirmedPlayers.Clear();

            // 1. 数据一致性检查和清理
            var activePlayerIds = _playerManager.GetActivePlayerIds();
            Log.Info($"[{_logName}] HandleRoundStart: {activePlayerIds.Count} active players");

            // 1.1. 检查并清理无效的玩家数据
            var validPlayerIds = new List<long>();
            foreach (var playerId in activePlayerIds)
            {
                var playerData = _playerManager.GetPlayerData(playerId);
                if (playerData != null && !playerData.IsEliminated)
                {
                    validPlayerIds.Add(playerId);
                    Log.Info($"[{_logName}] Valid player: {playerId}, Health: {playerData.Health}");
                }
                else
                {
                    Log.Warning($"[{_logName}] Invalid player data for {playerId}, skipping");
                }
            }

            // 更新活跃玩家列表
            activePlayerIds = validPlayerIds;

            // 1.2. 保存所有有效玩家的棋盘数据（在创建新实例前）
            foreach (var playerId in activePlayerIds)
            {
                var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                if (instance != null)
                {
                    var entities = instance.CheckerBoard.GetPlayerEntities(playerId);
                    Log.Info($"[{_logName}] Player {playerId} has {entities.Count} entities to save");

                    // 保存当前棋盘数据用于恢复到新实例
                    _playerManager.SavePlayerBoardData(playerId, entities);

                    // 保存上一回合结束时的棋盘数据用于对手查看
                    _playerManager.SavePlayerPreviousRoundBoardData(playerId, entities);
                }
                else
                {
                    Log.Warning($"[{_logName}] No instance found for player {playerId} when saving board data");
                }
            }

            // 2. 对手配对（只为有效玩家配对）
            if (activePlayerIds.Count < 2)
            {
                Log.Warning($"[{_logName}] Not enough active players ({activePlayerIds.Count}) for pairing");
                return;
            }

            var opponentPairs = _opponentPairManager.PairOpponents(activePlayerIds, _playerManager);
            Log.Info($"[{_logName}] Created {opponentPairs.Count} opponent pairs");

            // 2.1. 设置玩家对手关系到PlayerManager
            _playerManager.SetPlayerOpponents(opponentPairs);

            // 3. 创建战斗实例
            _instanceManager.CreateInstances(opponentPairs);

            // 4. 恢复所有玩家的棋盘数据（在创建新实例后）
            foreach (var playerId in activePlayerIds)
            {
                var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                if (instance != null)
                {
                    RestorePlayerBoardDataToInstance(playerId);
                }
                else
                {
                    Log.Warning($"[{_logName}] No instance found for player {playerId} when restoring board data");
                }
            }

            // 5. 清理所有实例中的孤立实体
            foreach (var playerId in activePlayerIds)
            {
                var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                if (instance != null)
                {
                    instance.CheckerBoard.CleanupOrphanedEntities();
                    Log.Debug($"[{_logName}] Cleaned orphaned entities for player {playerId}");
                }
            }

            // 6. 清空所有玩家的临时位（策划要求：准备阶段结束时清空临时位）
            foreach (var playerId in activePlayerIds)
            {
                var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                instance?.CheckerBoard.ClearPlayerTemporarySlots(playerId);
            }

            // 7. 重置玩家准备状态
            _playerManager.ResetPlayersReadyStatus();

            Log.Info($"[{_logName}] Round started with {_instanceManager.InstanceCount} battle instances");
        }

        /// <summary>
        /// 恢复玩家的棋盘数据到当前战斗实例中
        /// 根据玩家在当前实例中的位置分配正确的区域，确保区域一致性
        /// </summary>
        private void RestorePlayerBoardDataToInstance(long playerId)
        {
            var instance = _instanceManager.GetInstanceByPlayerId(playerId);
            if (instance == null)
            {
                Log.Warning($"[{_logName}] No instance found for player {playerId} when restoring board data");
                return;
            }

            var boardData = _playerManager.GetPlayerBoardData(playerId);
            if (boardData.Count == 0)
            {
                return;
            }

            // 检查是否已经恢复过数据（避免重复调用）
            var existingEntities = instance.CheckerBoard.GetPlayerEntities(playerId);
            if (existingEntities.Count > 0)
            {
                Log.Warning($"[{_logName}] Player {playerId} already has {existingEntities.Count} entities in instance, skipping restore");
                return;
            }

            // 确定玩家在当前实例中应该使用的区域
            bool useMyArea = ShouldUseMyArea(playerId);
            int targetAreaStart = useMyArea ? BattleConfig.Board.MyAreaStart : BattleConfig.Board.EnemyAreaStart;
            int targetAreaEnd = useMyArea ? BattleConfig.Board.MyAreaEnd : BattleConfig.Board.EnemyAreaEnd;

            Log.Info($"[{_logName}] Restoring player {playerId} to {(useMyArea ? "My" : "Enemy")} area (rows {targetAreaStart}-{targetAreaEnd}) based on current instance position");

            // 收集目标区域的可用位置
            var availablePositions = new List<(int Row, int Col)>();
            for (int row = targetAreaStart; row <= targetAreaEnd; row++)
            {
                for (int col = 1; col <= BattleConfig.Board.ColumnCount; col++)
                {
                    if (!instance.CheckerBoard.HasEntityAt(row, col))
                    {
                        availablePositions.Add((row, col));
                    }
                }
            }

            // 按原有的相对位置顺序恢复（保持棋子的相对排列）
            var sortedBoardData = boardData.OrderBy(e => e.GridX).ThenBy(e => e.GridY).ToList();

            for (int i = 0; i < sortedBoardData.Count && i < availablePositions.Count; i++)
            {
                var entityData = sortedBoardData[i];
                var targetPosition = availablePositions[i];

                // 在目标区域中重新创建实体
                var newEntity = instance.CheckerBoard.CreateAndPlaceEntity(
                    entityData.ConfigId,
                    playerId,
                    targetPosition.Row,
                    targetPosition.Col
                );

                // 保持星级（合成进度）
                if (newEntity.EntityId != 0)
                {
                    newEntity.StarLevel = entityData.StarLevel;

                    // 计算GridID用于日志
                    var originalGridId = (entityData.GridX - 1) * 6 + entityData.GridY;
                    var newGridId = (targetPosition.Row - 1) * 6 + targetPosition.Col;
                    Log.Info($"[{_logName}] Restored entity {newEntity.EntityId}: ({entityData.GridX},{entityData.GridY})->({targetPosition.Row},{targetPosition.Col}), GridID:{originalGridId}->{newGridId}, ConfigID:{newEntity.ConfigId}, StarLevel:{newEntity.StarLevel}");
                }
            }

            // 如果棋子数量超过可用位置，记录警告
            if (sortedBoardData.Count > availablePositions.Count)
            {
                Log.Warning($"[{_logName}] Player {playerId} has {sortedBoardData.Count} entities but only {availablePositions.Count} available positions in target area");
            }

            Log.Info($"[{_logName}] Restored board: player:{playerId} entities:{Math.Min(sortedBoardData.Count, availablePositions.Count)}/{boardData.Count}");
        }

        /// <summary>
        /// 处理准备阶段（包含buff选择、英雄生成、自由操作）
        /// </summary>
        private void HandlePreparationPhase()
        {
            Log.Info($"[{_logName}] Preparation phase started");

            // 根据配置决定是否有Buff选择环节
            if (_battleStateManager.HasBuffSelectionThisRound)
            {
                // Buff选项已经在StateRoundStart阶段生成，这里只需要为机器人自动选择
                AutoSelectBuffForBots();
            }

            // 注意：英雄生成已经在StateRoundStart阶段完成，这里不再重复生成
            // GenerateHeroesForAllPlayers(); // 已移动到StateRoundStart阶段;

            // 为机器人自动准备
            AutoReadyBots();

            // 进入自由操作阶段（玩家可以进行英雄合成、移动等操作）
            Log.Info($"[{_logName}] Free operation phase started");
        }

        /// <summary>
        /// 处理游戏结束逻辑
        /// </summary>
        private void HandleGameOver()
        {
            Log.Info($"[{_logName}] Game over processing");

            // 获取最终排名
            var activePlayerIds = _playerManager.GetActivePlayerIds();
            var eliminatedPlayerIds = _playerManager.GetEliminatedPlayerIds();

            // 确定获胜者
            long winnerId = 0;
            if (activePlayerIds.Count == 1)
            {
                winnerId = activePlayerIds[0];
                Log.Info($"[{_logName}] Game winner: Player {winnerId}");
            }
            else if (activePlayerIds.Count == 0)
            {
                Log.Warning($"[{_logName}] No active players remaining - unexpected game state");
            }

            // 注意：不在这里发送BattleEnd通知
            // BattleEnd通知将在所有RoundBattleEnd发送完成后发送
            // 这确保了正确的消息时序

            // 标记游戏结束，但不立即清理资源
            // 资源清理将由BattleService在适当时机进行
            Log.Info($"[{_logName}] Game over processing completed, waiting for cleanup signal");
        }

        /// <summary>
        /// 为机器人自动选择Buff（简单实现）
        /// </summary>
        private void AutoSelectBuffForBots()
        {
            var playerIds = _playerManager.GetAllPlayerIds();
            foreach (var playerId in playerIds)
            {
                // 检查是否是机器人（UID以9开头）
                if (playerId.ToString().StartsWith("9"))
                {
                    // 获取已经生成的Buff选项（不重新生成，避免覆盖真实玩家的选项）
                    var buffOptions = _buffManager.GetPlayerBuffOptions(playerId);

                    if (buffOptions.Count > 0)
                    {
                        var selectedBuff = buffOptions[0].BuffId; // 选择第一个可用Buff
                        bool success = _buffManager.PlayerSelectBuff(playerId, selectedBuff);
                        if (success)
                        {
                            Log.Info($"[{_logName}] Auto-selected buff {selectedBuff} for bot player {playerId}");

                            // 为机器人选择buff后立即生成英雄
                            var newHeroes = GenerateHeroesForPlayer(playerId);
                            Log.Info($"[{_logName}] Generated {newHeroes.Count} new heroes for bot player {playerId} after buff selection");
                        }
                        else
                        {
                            Log.Warning($"[{_logName}] Failed to select buff {selectedBuff} for bot player {playerId}");
                        }
                    }
                    else
                    {
                        Log.Info($"[{_logName}] No buff options available for bot player {playerId}");
                    }
                }
            }
        }



        /// <summary>
        /// 强制所有玩家准备完毕（超时处理）
        /// </summary>
        private void ForceAllPlayersReady()
        {
            var playerIds = _playerManager.GetAllPlayerIds();

            // 注意：buff选择的强制处理现在由独立的BuffSelectionTimeoutEvent处理
            // 这里只处理准备状态的强制设置

            // 强制所有玩家准备完毕
            foreach (var playerId in playerIds)
            {
                if (!_playerManager.IsPlayerReady(playerId))
                {
                    _playerManager.SetPlayerReady(playerId, true);
                    Log.Info($"[{_logName}] Force ready for player {playerId} due to preparation timeout");
                }
            }
        }

        /// <summary>
        /// 为机器人自动处理战斗结果（优化实现 - 立即执行）
        /// </summary>
        private async void AutoHandleBotBattles()
        {
            // 机器人vs机器人的战斗立即结算，机器人vs真人的战斗等待真人操作
            var playerIds = _playerManager.GetAllPlayerIds();
            var realPlayerId = playerIds.FirstOrDefault(id => !id.ToString().StartsWith("9"));
            var processedInstances = new HashSet<string>(); // 防止重复处理同一个实例

            // 立即处理机器人vs机器人的战斗
            foreach (var playerId in playerIds)
            {
                if (playerId.ToString().StartsWith("9"))
                {
                    var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                    if (instance != null && !instance.IsFinished)
                    {
                        var instanceId = instance.InstanceId;
                        var opponentId = _playerManager.GetPlayerOpponent(playerId);

                        // 只处理机器人vs机器人的战斗，立即结算
                        if (opponentId.ToString().StartsWith("9"))
                        {
                            // 防止同一个实例被处理多次
                            if (!processedInstances.Contains(instanceId))
                            {
                                processedInstances.Add(instanceId);

                                // 机器人vs机器人：随机胜负，立即结算
                                bool bot1Wins = new Random().Next(2) == 0;
                                Log.Info($"[{_logName}] Auto EndBattle for bot {playerId} vs bot {opponentId}, random result: bot {playerId} wins = {bot1Wins}");

                                // 为两个机器人都发送EndBattleReq
                                var (_, _) = HandleBattleEnd((ulong)playerId, bot1Wins);
                                var (_, _) = HandleBattleEnd((ulong)opponentId, !bot1Wins);
                            }
                        }
                        else
                        {
                            // 机器人vs真实玩家：等待真实玩家的结果
                            Log.Info($"[{_logName}] Bot {playerId} vs real player {opponentId}, waiting for real player result");
                        }
                    }
                }
            }

            // 机器人vs真人的战斗不需要额外的超时处理
            // 系统的StateBattleInProgress状态本身有60秒+5秒缓冲的超时机制
            // 如果真人一直不操作，系统会在65秒后自动强制结算
            Log.Info($"[{_logName}] Bot vs real player battles will be handled by system timeout (65s)");
        }

        /// <summary>
        /// 获取玩家最终阵容
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>玩家最终阵容英雄信息</returns>
        private List<PBBattleHeroInfo> GetPlayerFinalLineup(long playerId)
        {
            var finalHeroes = new List<PBBattleHeroInfo>();

            try
            {
                // 从战斗实例管理器获取玩家的最终阵容
                var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                if (instance != null)
                {
                    var entities = instance.CheckerBoard.GetAllEntities();
                    foreach (var entity in entities)
                    {
                        // 从玩家阵容中获取英雄的局外养成信息（Level和AwakeLevel）
                        var lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, entity.ConfigId);

                        finalHeroes.Add(new PBBattleHeroInfo
                        {
                            Id = entity.ConfigId,
                            Level = lineupHeroInfo?.Level ?? 1, // 使用阵容中的真实等级
                            StarLevel = entity.StarLevel, // 使用棋盘单位的实际星级（局内合成）
                            AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0 // 使用阵容中的真实觉醒等级
                        });
                    }
                }

                Log.Info($"[{_logName}] Player {playerId} final lineup: {finalHeroes.Count} heroes");
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Failed to get final lineup for player {playerId}: {ex.Message}");
            }

            return finalHeroes;
        }

        /// <summary>
        /// 延迟清理资源 - 给GameServer足够时间处理BattleEnd通知
        /// </summary>
        private void ScheduleDelayedCleanup()
        {
            Log.Info($"[{_logName}] Scheduling delayed cleanup in 5 seconds");

            // 使用Timer延迟5秒后清理资源
            var cleanupTimer = new System.Threading.Timer((_) =>
            {
                try
                {
                    Log.Info($"[{_logName}] Executing delayed cleanup");

                    // 通知BattleService清理这个战斗
                    BattleService.CleanupBattle(BattleId);
                }
                catch (Exception ex)
                {
                    Log.Error($"[{_logName}] Error during delayed cleanup: {ex.Message}");
                }
            }, null, TimeSpan.FromSeconds(5), Timeout.InfiniteTimeSpan);
        }

        /// <summary>
        /// 清理场景资源
        /// </summary>
        private void CleanupScene()
        {
            Log.Info($"[{_logName}] Cleaning up scene resources");

            // 清理所有战斗实例
            _instanceManager.Clear();

            // 清理玩家数据
            _playerManager.Clear();

            // 清理Buff数据
            _buffManager.Clear();

            // 清理棋盘
            _checkerBoard.Clear();

            // 清理状态管理器
            _battleStateManager.Clear();

            Log.Info($"[{_logName}] Scene cleanup completed");
        }

        /// <summary>
        /// 通知GameServer游戏结束
        /// </summary>
        private void NotifyGameServerOnGameOver(long winnerId, List<long> eliminatedPlayerIds)
        {
            Log.Info($"[{_logName}] Notifying GameServer: Game over, winner: {winnerId}");

            // 发送整场战斗结束通知
            NotifyGameServerBattleEnd();
        }

        /// <summary>
        /// 获取所有玩家ID（用于清理）
        /// </summary>
        public List<long> GetAllPlayerIds()
        {
            return _playerManager.GetAllPlayerIds();
        }

        /// <summary>
        /// 所有玩家准备就绪事件处理
        /// </summary>
        private void OnAllPlayersReady(AllPlayersReadyEvent evt)
        {
            Log.Info($"[{_logName}] All players are ready, transitioning to next state");

            // 根据当前状态决定下一个状态
            var nextState = _battleStateManager.CurrentState switch
            {
                BattleState.StatePreparation => BattleState.StateBattleStarting,
                _ => BattleState.StateNone
            };

            if (nextState != BattleState.StateNone)
            {
                _battleStateManager.SetState(nextState);
            }
            else
            {
                Log.Warning($"[{_logName}] Unexpected state when all players ready: {_battleStateManager.CurrentState}");
            }
        }

        /// <summary>
        /// 玩家淘汰事件处理
        /// </summary>
        private void OnPlayerEliminated(PlayerEliminatedEvent evt)
        {
            Log.Info($"[{_logName}] Player {evt.PlayerId} has been eliminated");

            // 清理该玩家的所有实体
            ClearPlayerEntities(evt.PlayerId);

            // 清理该玩家的所有Buff
            _buffManager.ClearPlayerBuffs(evt.PlayerId);
        }

        /// <summary>
        /// 游戏结束事件处理
        /// </summary>
        private void OnGameOver(GameOverEvent evt)
        {
            Log.Info($"[{_logName}] Game over! Winner: Player {evt.WinnerId}");

            // 设置游戏结束状态
            _battleStateManager.SetState(BattleState.StateGameOver);

            // 延迟清理资源，给GameServer足够时间处理BattleEnd通知
            ScheduleDelayedCleanup();
        }

        /// <summary>
        /// 实体创建事件处理
        /// </summary>
        private void OnEntityCreated(EntityCreatedEvent evt)
        {
            Log.Info($"[{_logName}] Entity {evt.Entity.EntityId} created at ({evt.Row},{evt.Column})");
        }

        /// <summary>
        /// 实体移动事件处理
        /// </summary>
        private void OnEntityMoved(EntityMovedEvent evt)
        {
            Log.Info($"[{_logName}] Entity {evt.Entity.EntityId} moved from ({evt.FromRow},{evt.FromColumn}) to ({evt.Row},{evt.Column})");
        }

        /// <summary>
        /// 实体合并事件处理
        /// </summary>
        private void OnEntityMerged(EntityMergedEvent evt)
        {
            Log.Info($"[{_logName}] Entity {evt.SourceEntity.EntityId} merged with {evt.TargetEntity.EntityId} at ({evt.TargetRow},{evt.TargetColumn})");
        }

        /// <summary>
        /// 实体移除事件处理
        /// </summary>
        private void OnEntityRemoved(EntityRemovedEvent evt)
        {
            Log.Info($"[{_logName}] Entity {evt.Entity.EntityId} removed from ({evt.Row},{evt.Column})");
        }


        /// <summary>
        /// 为所有活跃玩家生成Buff选项
        /// </summary>
        private void GenerateBuffOptionsForAllPlayers()
        {
            Log.Info($"[{_logName}] Generating buff options for all players");

            // 获取所有活跃玩家
            foreach (var playerId in _playerManager.GetActivePlayerIds())
            {
                // 获取玩家阵容
                List<int> lineup = _playerManager.GetPlayerLineup(playerId);

                // 使用BuffManager生成Buff选项
                var buffOptions = _buffManager.GenerateBuffOptions(playerId, lineup, _checkerBoard);

                // 记录日志并通知GameServer
                if (buffOptions.Count > 0)
                {
                    var buffIds = string.Join(", ", buffOptions.Select(b => b.BuffId));
                    Log.Info($"[{_logName}] Generated {buffOptions.Count} buff options for player {playerId}: [{buffIds}]");
                }
                else
                {
                    Log.Warning($"[{_logName}] Failed to generate buff options for player {playerId}");
                }
            }
        }

        /// <summary>
        /// 为指定玩家处理Buff选择
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="buffId">选择的BuffID</param>
        /// <returns>选择是否成功</returns>
        public bool HandleBuffSelection(long playerId, int buffId)
        {
            // 只有在准备阶段才能处理Buff选择
            if (_battleStateManager.CurrentState != BattleState.StatePreparation)
            {
                Log.Warning($"[{_logName}] Cannot handle buff selection in state {_battleStateManager.CurrentState}");
                return false;
            }

            // 使用BuffManager处理玩家选择
            if (_buffManager.PlayerSelectBuff(playerId, buffId))
            {
                Log.Info($"[{_logName}] Player {playerId} selected buff {buffId}");
                return true;
            }
            else
            {
                Log.Warning($"[{_logName}] Failed to select buff {buffId} for player {playerId}");
                return false;
            }
        }

        /// <summary>
        /// 为所有活跃玩家生成英雄
        /// </summary>
        private void GenerateHeroesForAllPlayers()
        {
            // 根据回合获取英雄生成数量
            var heroCount = BattleConfig.GameRules.GetHeroCountForRound(_battleStateManager.RoundCount);
            Log.Info($"[{_logName}] Generating {heroCount} heroes for all players in round {_battleStateManager.RoundCount}");

            // 获取所有活跃玩家
            foreach (var playerId in _playerManager.GetActivePlayerIds())
            {
                GenerateHeroesForPlayer(playerId);
            }
        }

        /// <summary>
        /// 为单个玩家生成英雄（用于Buff选择后的增量生成）
        /// 使用玩家固定的绝对GridID区域，不根据实例位置动态分配
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>生成的英雄信息列表</returns>
        private List<PBCheckerBoard> GenerateHeroesForPlayer(long playerId)
        {
            var newHeroes = new List<PBCheckerBoard>();

            // 根据回合获取英雄生成数量
            var heroCount = BattleConfig.GameRules.GetHeroCountForRound(_battleStateManager.RoundCount);

            // 获取玩家阵容
            List<int> lineup = _playerManager.GetPlayerLineup(playerId);

            // 获取玩家所在的战斗实例
            var instance = _instanceManager.GetInstanceByPlayerId(playerId);
            if (instance != null)
            {
                // 使用玩家固定的绝对GridID区域，不根据实例位置分配
                // 根据玩家ID确定其固定的GridID区域（1-30 或 31-60）
                bool useMyArea = ShouldUseMyArea(playerId);

                // 在对应的战斗实例中生成英雄
                var entities = instance.CheckerBoard.GenerateHeroesFromLineup(playerId, lineup, heroCount, useMyArea);

                // 将生成的英雄转换为PBCheckerBoard格式
                // 只返回真正放置到棋盘上的英雄，临时位的英雄不返回给客户端
                foreach (var entity in entities)
                {
                    if (entity.EntityId != 0 && entity.GridX > 0 && entity.GridY > 0) // 有效实体且在棋盘上
                    {
                        // 从玩家阵容中获取英雄的局外养成信息（Level和AwakeLevel）
                        var lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, entity.ConfigId);

                        // 计算格子ID（服务器原始坐标，不进行转换）
                        var gridId = (entity.GridX - 1) * 6 + entity.GridY;

                        newHeroes.Add(new PBCheckerBoard
                        {
                            GridID = gridId,
                            Hero = new PBBattleHeroInfo
                            {
                                Id = entity.ConfigId,
                                Level = lineupHeroInfo?.Level ?? 1, // 使用阵容中的真实等级
                                StarLevel = 1, // 新生成的英雄都是1星
                                AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0 // 使用阵容中的真实觉醒等级
                            }
                        });

                        // 移除冗余的调试日志
                    }
                    // 临时位英雄不返回给客户端，静默跳过
                }

                var placedCount = newHeroes.Count;
                var totalCount = entities.Count;
                Log.Info($"[{_logName}] Generated {totalCount} heroes for player {playerId}: {placedCount} placed on board, {totalCount - placedCount} in temporary slots");
            }
            else
            {
                Log.Warning($"[{_logName}] No battle instance found for player {playerId}");
            }

            return newHeroes;
        }

        /// <summary>
        /// 为所有玩家应用战斗开始时的Buff效果
        /// </summary>
        private void ApplyBuffsForAllPlayers()
        {
            Log.Info($"[{_logName}] Applying battle start buffs for all players");

            // 获取所有活跃玩家
            foreach (var playerId in _playerManager.GetActivePlayerIds())
            {
                // 使用BuffManager应用战斗开始Buff
                _buffManager.ApplyBattleStartBuffs(playerId);
            }
        }

        /// <summary>
        /// 启动所有战斗实例
        /// </summary>
        private void StartAllBattleInstances()
        {
            Log.Info($"[{_logName}] Starting all battle instances");
            _instanceManager.StartAllBattles();
        }

        /// <summary>
        /// 处理战斗结果
        /// </summary>
        private void ProcessBattleResults()
        {
            Log.Info($"[{_logName}] Processing battle results");

            // 获取所有已完成的战斗实例结果
            var finishedInstances = _instanceManager.GetFinishedInstances();
            bool isGameEnding = false;

            foreach (var instance in finishedInstances)
            {
                var result = instance.Result;
                if (result != null)
                {
                    if (result.WinnerId == 0)
                    {
                        // 平局，双方都扣1血
                        foreach (var playerId in instance.PlayerIds)
                        {
                            _playerManager.ReducePlayerHealth(playerId, 1);
                            Log.Info($"[{_logName}] Player {playerId} lost 1 health due to draw");
                        }
                        // 发送平局结果通知
                        NotifyGameServerRoundBattleEnd(0, 0);
                    }
                    else
                    {
                        // 有胜负，败方扣1血
                        var loserId = result.LoserId;
                        _playerManager.ReducePlayerHealth(loserId, 1);
                        Log.Info($"[{_logName}] Player {loserId} lost 1 health, winner: {result.WinnerId}");

                        // 发送战斗结果通知
                        NotifyGameServerRoundBattleEnd(result.WinnerId, loserId);
                    }
                }
            }

            // 检查游戏是否即将结束
            var activePlayerIds = _playerManager.GetActivePlayerIds();
            isGameEnding = activePlayerIds.Count <= 1;

            // 如果游戏即将结束，在发送完所有RoundBattleEnd后发送BattleEnd
            if (isGameEnding && !_battleEndSent)
            {
                Log.Info($"[{_logName}] Game ending detected, will send BattleEnd after RoundBattleEnd");

                // 获取获胜者信息
                long winnerId = activePlayerIds.Count == 1 ? activePlayerIds[0] : 0;
                var eliminatedPlayerIds = _playerManager.GetEliminatedPlayerIds();

                // 发送BattleEnd通知（在RoundBattleEnd之后）
                NotifyGameServerOnGameOver(winnerId, eliminatedPlayerIds);
                _battleEndSent = true; // 标记已发送，防止重复
            }

            // 转入回合结算阶段（合并了原来的StateEliminationCheck）
            // 避免重复设置相同状态
            if (_battleStateManager.CurrentState != BattleState.StateRoundSettlement)
            {
                _battleStateManager.SetState(BattleState.StateRoundSettlement);
            }
        }

        /// <summary>
        /// 检查玩家淘汰状态
        /// </summary>
        private void CheckPlayersElimination()
        {
            Log.Info($"[{_logName}] Checking players elimination");

            // 检查血量为0的玩家并淘汰
            var allPlayerIds = _playerManager.GetAllPlayerIds();
            var eliminatedPlayers = new List<long>();

            foreach (var playerId in allPlayerIds)
            {
                if (!_playerManager.IsPlayerEliminated(playerId))
                {
                    var health = _playerManager.GetPlayerHealth(playerId);
                    if (health <= 0)
                    {
                        _playerManager.EliminatePlayer(playerId);
                        eliminatedPlayers.Add(playerId);
                        Log.Info($"[{_logName}] Player {playerId} eliminated (health: {health})");
                    }
                }
            }

            // 获取剩余活跃玩家
            var activePlayerIds = _playerManager.GetActivePlayerIds();
            Log.Info($"[{_logName}] Active players remaining: {activePlayerIds.Count}");

            // 检查游戏是否结束
            if (activePlayerIds.Count <= 1)
            {
                // 游戏结束 - 设置GameOver状态（BattleEnd已在ProcessBattleResults中发送）
                if (_battleStateManager.CurrentState != BattleState.StateGameOver)
                {
                    Log.Info($"[{_logName}] Game ending, transitioning to StateGameOver");
                    _battleStateManager.SetState(BattleState.StateGameOver);
                }
            }
            else
            {
                // 等待玩家确认结算完成，不自动开始新回合
                // 新回合将在收到所有玩家的EnterBattleReq后开始
                Log.Info($"[{_logName}] Waiting for all players to confirm round settlement before starting new round");

                // 机器人立即确认结算
                AutoConfirmBotsRoundSettlement();
            }
        }

        /// <summary>
        /// 清理指定玩家的所有实体
        /// </summary>
        private void ClearPlayerEntities(long playerId)
        {
            var playerEntities = _checkerBoard.GetPlayerEntities(playerId);
            foreach (var entity in playerEntities)
            {
                _checkerBoard.RemoveEntity(entity.GridX, entity.GridY);
            }
            Log.Info($"[{_logName}] Cleared {playerEntities.Count} entities for player {playerId}");
        }


        /// <summary>
        /// 重写基类的 OnTick 方法，实现场景每帧更新逻辑
        /// </summary>
        /// <param name="nDeltaTime">上一帧到当前帧的时间间隔（毫秒）</param>
        protected override void OnTick(int nDeltaTime)
        {
            // 委托状态管理器更新倒计时和状态
            _battleStateManager.Update(nDeltaTime);
        }

        /// <summary>
        /// 公开的Tick方法，供SceneManager调用
        /// </summary>
        /// <param name="nDeltaTime">时间增量（毫秒）</param>
        public void Tick(int nDeltaTime)
        {
            OnTick(nDeltaTime);
        }

        /// <summary>
        /// 设置玩家准备状态
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="isReady">是否准备</param>
        /// <param name="moves">准备前要同步的移动操作</param>
        public void SetPlayerReady(long playerId, bool isReady, IList<PBMoveOperation> moves = null)
        {
            // 验证当前状态是否允许设置准备状态
            if (_battleStateManager.CurrentState != BattleState.StatePreparation)
            {
                Log.Warning($"[{_logName}] Cannot set player ready in state {_battleStateManager.CurrentState}");
                return;
            }

            // 先处理移动操作，确保服务器棋盘状态与客户端同步
            if (moves != null && moves.Count > 0)
            {
                Log.Info($"[{_logName}] Processing {moves.Count} move operations before setting player {playerId} ready");
                ProcessMoveOperations(playerId, moves);
            }

            // 使用PlayerManager设置玩家准备状态
            _playerManager.SetPlayerReady(playerId, isReady);
            Log.Info($"[{_logName}] Player {playerId} set ready status to {isReady}");

            // 检查是否所有玩家都准备完毕
            CheckAllPlayersReady();
        }

        /// <summary>
        /// 检查是否所有玩家都准备完毕，如果是则立即开始战斗
        /// </summary>
        private void CheckAllPlayersReady()
        {
            if (_battleStateManager.CurrentState != BattleState.StatePreparation)
            {
                return;
            }

            var allPlayerIds = _playerManager.GetAllPlayerIds();
            var readyCount = 0;

            foreach (var playerId in allPlayerIds)
            {
                if (_playerManager.IsPlayerReady(playerId))
                {
                    readyCount++;
                }
            }

            // 先让机器人自动准备（如果还没准备的话）
            AutoReadyBots();

            // 重新统计准备人数
            readyCount = 0;
            foreach (var playerId in allPlayerIds)
            {
                if (_playerManager.IsPlayerReady(playerId))
                {
                    readyCount++;
                }
            }

            // 如果所有玩家都准备完毕，立即开始战斗
            if (readyCount >= allPlayerIds.Count)
            {
                Log.Info($"[{_logName}] All players ready! Starting battle immediately");
                _battleStateManager.SetState(BattleState.StateBattleStarting);
            }
            // 移除等待日志，减少冗余输出
        }

        /// <summary>
        /// 机器人自动准备
        /// </summary>
        private void AutoReadyBots()
        {
            var allPlayerIds = _playerManager.GetAllPlayerIds();
            var readyCount = 0;
            foreach (var playerId in allPlayerIds)
            {
                // 检查是否是机器人（UID以9开头）且未准备
                if (playerId.ToString().StartsWith("9") && !_playerManager.IsPlayerReady(playerId))
                {
                    _playerManager.SetPlayerReady(playerId, true);
                    readyCount++;
                }
            }
            // 只在有机器人准备时才输出日志
            if (readyCount > 0)
            {
                Log.Info($"[{_logName}] Auto-ready {readyCount} additional bots");
            }
        }

        // 回合确认计数器
        private readonly HashSet<long> _roundConfirmedPlayers = new HashSet<long>();

        // 战斗结束请求跟踪：实例ID -> 已发送EndBattleReq的玩家ID集合
        private readonly Dictionary<string, HashSet<long>> _instanceEndBattleRequests = new Dictionary<string, HashSet<long>>();

        /// <summary>
        /// 处理玩家回合确认（结算完成后的EnterBattleReq）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        public void HandleRoundConfirmation(long playerId)
        {
            // 只在StateRoundSettlement状态下处理回合确认（合并了原来的StateEliminationCheck）
            if (_battleStateManager.CurrentState != BattleState.StateRoundSettlement)
            {
                Log.Warning($"[{_logName}] Cannot handle round confirmation in state {_battleStateManager.CurrentState}");
                return;
            }

            // 添加到确认列表
            _roundConfirmedPlayers.Add(playerId);
            Log.Info($"[{_logName}] Player {playerId} confirmed round settlement, count: {_roundConfirmedPlayers.Count}");

            // 如果是真实玩家确认，立即让所有机器人确认
            if (!playerId.ToString().StartsWith("9"))
            {
                Log.Info($"[{_logName}] Real player {playerId} confirmed, auto-confirming all bots");
                AutoConfirmBotsRoundSettlement();
            }

            // 检查是否所有玩家都确认了
            var allPlayerIds = _playerManager.GetAllPlayerIds();
            if (_roundConfirmedPlayers.Count >= allPlayerIds.Count)
            {
                Log.Info($"[{_logName}] All players confirmed round settlement, starting new round");

                // 清空确认列表，准备下一回合
                _roundConfirmedPlayers.Clear();

                // 重置玩家准备状态
                foreach (var pid in allPlayerIds)
                {
                    _playerManager.SetPlayerReady(pid, false);
                }

                // 开始新回合
                _battleStateManager.StartNewRound();
            }
            else
            {
                var remainingCount = allPlayerIds.Count - _roundConfirmedPlayers.Count;
                Log.Info($"[{_logName}] Still waiting for {remainingCount} players to confirm round settlement");
            }
        }

        /// <summary>
        /// 机器人自动确认回合结算
        /// </summary>
        private void AutoConfirmBotsRoundSettlement()
        {
            var allPlayerIds = _playerManager.GetAllPlayerIds();
            foreach (var playerId in allPlayerIds)
            {
                // 检查是否是机器人（UID以9开头）且还未确认
                if (playerId.ToString().StartsWith("9") && !_roundConfirmedPlayers.Contains(playerId))
                {
                    Log.Info($"[{_logName}] Auto-confirming round settlement for bot {playerId}");
                    _roundConfirmedPlayers.Add(playerId);
                }
            }
        }

        /// <summary>
        /// 强制所有玩家确认回合结算（超时处理）
        /// </summary>
        private void ForceAllPlayersConfirmRound()
        {
            var allPlayerIds = _playerManager.GetAllPlayerIds();
            foreach (var playerId in allPlayerIds)
            {
                if (!_roundConfirmedPlayers.Contains(playerId))
                {
                    Log.Info($"[{_logName}] Force confirming round settlement for player {playerId} due to timeout");
                    HandleRoundConfirmation(playerId);
                }
            }
        }

        /// <summary>
        /// 处理移动操作列表
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="moves">移动操作列表</param>
        private void ProcessMoveOperations(long playerId, IList<PBMoveOperation> moves)
        {
            var instance = _instanceManager.GetInstanceByPlayerId(playerId);
            if (instance == null)
            {
                Log.Warning($"[{_logName}] No battle instance found for player {playerId} in ProcessMoveOperations");
                return;
            }

            foreach (var move in moves)
            {
                // 直接使用绝对GridID转换为坐标
                var (fromRow, fromCol) = GridIdToCoord(move.FromGridId);
                var (toRow, toCol) = GridIdToCoord(move.ToGridId);

                // 执行移动操作
                bool moveSuccess = instance.CheckerBoard.MoveEntity(fromRow, fromCol, toRow, toCol);
                Log.Info($"[{_logName}] Player {playerId} move operation: GridID({move.FromGridId}->{move.ToGridId}) → Coord(({fromRow},{fromCol})->({toRow},{toCol})), success: {moveSuccess}");

                if (!moveSuccess)
                {
                    Log.Warning($"[{_logName}] Failed to execute move operation for player {playerId}: GridID({move.FromGridId}->{move.ToGridId}) → Coord(({fromRow},{fromCol})->({toRow},{toCol}))");
                }
            }
        }

        /// <summary>
        /// 处理玩家的英雄合成操作（包含移动和合成）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="fromGridId">源格子ID（绝对GridID 1-60）</param>
        /// <param name="toGridId">目标格子ID（绝对GridID 1-60）</param>
        /// <param name="moves">本次合成前发生的所有移动操作</param>
        /// <returns>合成结果</returns>
        public MergeHeroResp MergeHero(long playerId, int fromGridId, int toGridId, IList<PBMoveOperation> moves = null)
        {
            // 验证当前状态是否允许操作
            if (_battleStateManager.CurrentState != BattleState.StatePreparation)
            {
                Log.Warning($"[{_logName}] Cannot merge hero in state {_battleStateManager.CurrentState}");
                return new MergeHeroResp { Code = -10, From = fromGridId, To = toGridId, NewHero = null };
            }

            Log.Info($"[{_logName}] MergeHero operation: Player {playerId}, From GridID {fromGridId} → To GridID {toGridId}");
            if (moves != null && moves.Count > 0)
            {
                Log.Info($"[{_logName}] MergeHero with {moves.Count} move operations:");
                for (int i = 0; i < moves.Count; i++)
                {
                    Log.Info($"[{_logName}] - Move {i+1}: GridID {moves[i].FromGridId} → {moves[i].ToGridId}");
                }
            }
            else
            {
                Log.Info($"[{_logName}] MergeHero with no move operations");
            }

            // 直接路由到对应的战斗实例，使用绝对GridID
            return _instanceManager.MergeHero(playerId, fromGridId, toGridId, moves);
        }

        /// <summary>
        /// 处理玩家的Buff选择操作
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="buffId">选择的Buff ID</param>
        /// <returns>选择结果</returns>
        public SelectBufferResp SelectBuff(long playerId, int buffId)
        {
            // 验证当前状态是否允许选择Buff
            if (_battleStateManager.CurrentState != BattleState.StatePreparation)
            {
                Log.Warning($"[{_logName}] Cannot select buff in state {_battleStateManager.CurrentState}");
                return new SelectBufferResp { Code = -10 };
            }

            // 使用BuffManager处理Buff选择
            bool success = _buffManager.PlayerSelectBuff(playerId, buffId);

            var response = new SelectBufferResp
            {
                Code = success ? 0 : -1
            };

            // 如果Buff选择成功且当前是Buff回合，生成新英雄
            if (success && _battleStateManager.HasBuffSelectionThisRound)
            {
                var newHeroes = GenerateHeroesForPlayer(playerId);

                // 直接返回绝对GridID，不进行坐标转换
                response.NewHeroes.AddRange(newHeroes);
                Log.Info($"[{_logName}] Player {playerId} selected buff {buffId}, generated {newHeroes.Count} new heroes");

                // 验证英雄生成后的格子一致性
                var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                instance?.CheckerBoard.ValidateAndLogConsistency($"SelectBuff({playerId}, {buffId})");
            }

            return response;
        }

        /// <summary>
        /// 处理战斗结束（由客户端通过EndBattleReq触发）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="win">是否胜利</param>
        /// <returns>处理结果：(是否成功, 是否为最后回合)</returns>
        public (bool success, bool isEnd) HandleBattleEnd(ulong playerId, bool win)
        {
            try
            {
                // 获取玩家所在的战斗实例
                var instance = _instanceManager.GetInstanceByPlayerId((long)playerId);
                if (instance == null)
                {
                    Log.Warning($"[{_logName}] Player {playerId} not found in any battle instance");
                    return (false, false);
                }

                var instanceId = instance.InstanceId;
                var opponentId = _playerManager.GetPlayerOpponent((long)playerId);

                // 初始化实例的EndBattle请求跟踪
                if (!_instanceEndBattleRequests.ContainsKey(instanceId))
                {
                    _instanceEndBattleRequests[instanceId] = new HashSet<long>();
                }

                // 记录该玩家已发送EndBattleReq
                _instanceEndBattleRequests[instanceId].Add((long)playerId);
                Log.Info($"[{_logName}] Player {playerId} sent EndBattleReq (win: {win}), instance: {instanceId}");

                // 如果是真实玩家发送EndBattleReq，且对手是机器人，则自动为机器人发送相反结果
                if (!playerId.ToString().StartsWith("9") && opponentId.ToString().StartsWith("9"))
                {
                    // 真实玩家发送了结果，机器人自动发送相反结果
                    if (!_instanceEndBattleRequests[instanceId].Contains(opponentId))
                    {
                        _instanceEndBattleRequests[instanceId].Add(opponentId);
                        Log.Info($"[{_logName}] Auto EndBattle for bot {opponentId} vs real player {playerId}, bot result: {!win}");
                    }
                }

                // 检查同一实例的两名玩家是否都发送了EndBattleReq
                var instanceRequests = _instanceEndBattleRequests[instanceId];
                if (instanceRequests.Count >= 2)
                {
                    // 两名玩家都发送了EndBattleReq，确定胜负并推送结果
                    var winnerId = win ? (long)playerId : opponentId;
                    var loserId = win ? opponentId : (long)playerId;

                    // 标记战斗实例完成
                    instance.FinishBattle(winnerId);

                    Log.Info($"[{_logName}] Battle instance {instanceId} completed: Winner {winnerId}, Loser {loserId}");

                    // 清理该实例的跟踪数据
                    _instanceEndBattleRequests.Remove(instanceId);

                    // 检查是否所有战斗实例都完成了
                    bool allInstancesFinished = _instanceManager.AllInstancesFinished();
                    if (allInstancesFinished)
                    {
                        Log.Info($"[{_logName}] All battle instances finished, proceeding to settlement");
                        _battleStateManager.EndBattle();
                    }

                    // 检查是否为最后回合（游戏即将结束）
                    var activePlayerIds = _playerManager.GetActivePlayerIds();
                    bool isGameEnding = activePlayerIds.Count <= 1;

                    return (true, isGameEnding);
                }
                else
                {
                    Log.Info($"[{_logName}] Waiting for opponent {opponentId} to send EndBattleReq for instance {instanceId}");
                    return (true, false); // 还在等待对手，不是最后回合
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Error handling battle end: {ex.Message}");
                return (false, false);
            }
        }

        /// <summary>
        /// 检查玩家是否在战斗中
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>是否在战斗中</returns>
        public bool IsPlayerInBattle(long playerId)
        {
            return _instanceManager.GetInstanceByPlayerId(playerId) != null;
        }

        /// <summary>
        /// 检查场景中是否包含指定玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>是否包含该玩家</returns>
        public bool HasPlayer(long playerId)
        {
            return _playerManager.GetAllPlayerIds().Contains(playerId);
        }

        /// <summary>
        /// 添加实体到棋盘
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="configId">实体配置ID</param>
        /// <param name="row">行坐标</param>
        /// <param name="col">列坐标</param>
        /// <returns>创建的实体对象，或者失败时返回null</returns>
        public Entity AddEntity(long playerId, int configId, int row, int col)
        {
            // 验证玩家存在且未淘汰
            if (_playerManager.IsPlayerEliminated(playerId))
            {
                Log.Warning($"[{_logName}] Player {playerId} is eliminated in AddEntity");
                return null;
            }

            // 验证当前状态允许添加实体
            if (_battleStateManager.CurrentState != BattleState.StatePreparation)
            {
                Log.Warning($"[{_logName}] Cannot add entity in state {_battleStateManager.CurrentState}");
                return null;
            }

            // 使用CheckerBoard创建和放置实体
            Entity entity = _checkerBoard.CreateAndPlaceEntity(configId, playerId, row, col);
            if (entity != null && entity.EntityId != 0)
            {
                Log.Info($"[{_logName}] Player {playerId} added entity {configId} at ({row},{col})");
            }
            else
            {
                Log.Warning($"[{_logName}] Failed to add entity {configId} for player {playerId} at ({row},{col})");
            }

            return entity;
        }

        /// <summary>
        /// 移动实体或尝试合并实体
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="fromRow">源格子行坐标</param>
        /// <param name="fromCol">源格子列坐标</param>
        /// <param name="toRow">目标格子行坐标</param>
        /// <param name="toCol">目标格子列坐标</param>
        /// <returns>移动或合并是否成功</returns>
        public bool MoveEntity(long playerId, int fromRow, int fromCol, int toRow, int toCol)
        {
            // 验证玩家存在且未淘汰
            if (_playerManager.IsPlayerEliminated(playerId))
            {
                Log.Warning($"[{_logName}] Player {playerId} is eliminated in MoveEntity");
                return false;
            }

            // 验证当前状态允许移动实体
            if (_battleStateManager.CurrentState != BattleState.StatePreparation)
            {
                Log.Warning($"[{_logName}] Cannot move entity in state {_battleStateManager.CurrentState}");
                return false;
            }

            // 验证源格子上有实体
            if (!_checkerBoard.HasEntityAt(fromRow, fromCol))
            {
                Log.Warning($"[{_logName}] No entity at source position ({fromRow},{fromCol})");
                return false;
            }

            // 获取源格子上的实体并验证其所有权
            Entity sourceEntity = _checkerBoard.GetEntityAt(fromRow, fromCol);
            if (sourceEntity.OwnerId != playerId)
            {
                Log.Warning($"[{_logName}] Entity at ({fromRow},{fromCol}) does not belong to player {playerId}");
                return false;
            }

            // 验证目标位置是否合法
            if (!_checkerBoard.IsValidPosition(toRow, toCol))
            {
                Log.Warning($"[{_logName}] Invalid target position ({toRow},{toCol})");
                return false;
            }

            // 如果目标格子上有实体，尝试合并
            if (_checkerBoard.HasEntityAt(toRow, toCol))
            {
                Entity targetEntity = _checkerBoard.GetEntityAt(toRow, toCol);

                // 验证目标实体是否可以与源实体合并
                if (targetEntity.OwnerId == playerId &&
                    targetEntity.ConfigId == sourceEntity.ConfigId &&
                    targetEntity.StarLevel == sourceEntity.StarLevel &&
                    targetEntity.StarLevel < 3) // 限制星级上限
                {
                    // 使用CheckerBoard的合并方法
                    bool success = _checkerBoard.TryMergeEntities(fromRow, fromCol, toRow, toCol);
                    if (success)
                    {
                        Log.Info($"[{_logName}] Player {playerId} merged entities: ({fromRow},{fromCol}) -> ({toRow},{toCol})");
                    }
                    else
                    {
                        Log.Warning($"[{_logName}] Failed to merge entities: ({fromRow},{fromCol}) -> ({toRow},{toCol})");
                    }
                    return success;
                }

                // 其他情况下不允许移动到已有实体的格子
                Log.Warning($"[{_logName}] Cannot move to occupied position without merging: ({fromRow},{fromCol}) -> ({toRow},{toCol})");
                return false;
            }

            // 目标格子为空，移动实体
            bool moveSuccess = _checkerBoard.MoveEntity(fromRow, fromCol, toRow, toCol);
            if (moveSuccess)
            {
                Log.Info($"[{_logName}] Player {playerId} moved entity: ({fromRow},{fromCol}) -> ({toRow},{toCol})");
            }
            else
            {
                Log.Warning($"[{_logName}] Failed to move entity: ({fromRow},{fromCol}) -> ({toRow},{toCol})");
            }
            return moveSuccess;
        }



        /// <summary>
        /// 通知GameServer战斗状态变更
        /// </summary>
        private void NotifyGameServerOnStateChanged(BattleState oldState, BattleState newState, int countdown)
        {
            try
            {
                if (NatsClient.GameServiceClient != null)
                {
                    var req = new BattleStateChangeReq
                    {
                        BattleId = BattleId,
                        State = newState,
                        RemainTimeMs = countdown,
                        RoundCount = _battleStateManager?.RoundCount ?? 0
                    };

                    NatsClient.GameServiceClient.OnBattleStateChanged(req, _gameServerId).ConfigureAwait(false);
                    Log.Info($"[{_logName}] State change sent to GameServer: {oldState} -> {newState} (R{req.RoundCount})");
                }
                else
                {
                    Log.Error($"[{_logName}] CRITICAL: NATS client is NULL - cannot send state change!");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] CRITICAL: Failed to send state change to GameServer: {ex.Message}");
            }
        }





        /// <summary>
        /// 获取战斗ID
        /// </summary>
        public long GetBattleId()
        {
            return BattleId;
        }

        /// <summary>
        /// 获取所有玩家ID列表
        /// </summary>
        public List<long> GetPlayerIds()
        {
            return _playerManager.GetAllPlayerIds();
        }

        /// <summary>
        /// 获取活跃玩家ID列表（未淘汰）
        /// </summary>
        public List<long> GetActivePlayerIds()
        {
            return _playerManager.GetActivePlayerIds();
        }

        /// <summary>
        /// 获取玩家当前生命值
        /// </summary>
        public int GetPlayerHealth(long playerId)
        {
            return _playerManager.GetPlayerHealth(playerId);
        }

        /// <summary>
        /// 获取棋盘上的所有实体列表
        /// </summary>
        public List<Entity> GetBoardEntities()
        {
            // 直接使用CheckerBoard组件获取所有实体
            return _checkerBoard.GetAllEntities();
        }

        /// <summary>
        /// 获取指定玩家的棋盘实体
        /// </summary>
        public List<Entity> GetPlayerEntities(long playerId)
        {
            // 使用CheckerBoard组件获取指定玩家的实体
            return _checkerBoard.GetPlayerEntities(playerId);
        }

        /// <summary>
        /// 获取当前战斗状态
        /// </summary>
        public BattleState GetCurrentState()
        {
            return _battleStateManager.CurrentState;
        }

        /// <summary>
        /// 获取当前状态剩余时间(毫秒)
        /// </summary>
        public int GetStateRemainingTime()
        {
            return _battleStateManager.StateCountdown;
        }

        /// <summary>
        /// 清理场景资源
        /// </summary>
        public void CleanupResources()
        {
            Dispose();
        }

        /// <summary>
        /// 获取战斗状态管理器
        /// </summary>
        /// <returns>战斗状态管理器实例</returns>
        public BattleStateManager GetBattleStateManager()
        {
            return _battleStateManager;
        }

        /// <summary>
        /// 获取玩家管理器
        /// </summary>
        /// <returns>玩家管理器实例</returns>
        public PlayerManager GetPlayerManager()
        {
            return _playerManager;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 取消事件订阅
            UnregisterEventHandlers();

            // 释放各个组件资源
            _playerManager?.Dispose();
            _checkerBoard?.Dispose();
            _battleStateManager?.Dispose();
            _buffManager?.Dispose();

            Log.Info($"[{_logName}] Scene resources disposed");
        }

        /// <summary>
        /// 为与AI对战的玩家发送回合开始通知
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="hasBuffSelection">是否有Buff选择</param>
        private void SendRoundStartNotificationForAIBattle(long playerId, bool hasBuffSelection)
        {
            try
            {
                var req = new RoundStartReq
                {
                    Uid = (ulong)playerId
                };

                // 添加Buff选择（如果有的话）
                if (hasBuffSelection)
                {
                    var buffOptions = _buffManager.GetPlayerBuffOptions(playerId);
                    var buffIds = buffOptions.Select(b => b.BuffId).ToList();
                    req.Buffers.AddRange(buffIds);
                    Log.Info($"[{_logName}] RoundStart: Player {playerId} buff options: [{string.Join(", ", buffIds)}]");
                }

                // 添加玩家自己的棋盘数据（从保存的数据中获取）
                var playerBoardData = _playerManager.GetPlayerPreviousRoundBoardData(playerId);
                if (playerBoardData.Count > 0)
                {
                    var playerBoard = new PBPlayerBoard
                    {
                        Uid = (ulong)playerId
                    };

                    foreach (var entityData in playerBoardData)
                    {
                        // 使用绝对GridID，不进行坐标转换
                        var gridId = (entityData.GridX - 1) * 6 + entityData.GridY;

                        var lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, entityData.ConfigId);
                        playerBoard.BoardInfo.Add(new PBCheckerBoard
                        {
                            GridID = gridId, // 使用绝对GridID
                            Hero = new PBBattleHeroInfo
                            {
                                Id = entityData.ConfigId,
                                Level = lineupHeroInfo?.Level ?? 1,
                                StarLevel = entityData.StarLevel,
                                AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0
                            }
                        });
                    }

                    req.PlayerBoards.Add(playerBoard);
                    Log.Info($"[{_logName}] RoundStart self board: player:{playerId} heroes:{playerBoard.BoardInfo.Count}");
                }

                // 获取玩家的服务器ID并发送
                var playerServerId = _playerManager.GetPlayerServerId(playerId);
                NatsClient.GameServiceClient.RoundStart(req, playerServerId).ConfigureAwait(false);

                Log.Info($"[{_logName}] Sent RoundStart to Player {playerId} (vs AI) on GameServer {playerServerId}");
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Failed to send RoundStart notification for AI battle to player {playerId}: {ex.Message}");
            }
        }

        /// <summary>
        /// 通知GameServer回合开始
        /// </summary>
        private void NotifyGameServerRoundStart()
        {
            try
            {
                if (NatsClient.GameServiceClient == null)
                {
                    Log.Warning($"[{_logName}] NATS GameService client not available for RoundStart");
                    return;
                }

                // 获取当前回合的Buff选择（如果有的话）
                var currentRound = _battleStateManager.RoundCount;
                var hasBuffSelection = BattleConfig.Buff.HasBuffSelection(currentRound);

                // 只为活跃玩家发送通知，避免给已淘汰或不在战斗实例中的玩家发送
                var playerIds = _playerManager.GetActivePlayerIds();

                // 调试信息：显示所有玩家状态
                var allPlayerIds = _playerManager.GetAllPlayerIds();
                Log.Info($"[{_logName}] Player status: Total={allPlayerIds.Count}, Active={playerIds.Count}");
                foreach (var pid in allPlayerIds)
                {
                    var isEliminated = _playerManager.IsPlayerEliminated(pid);
                    var health = _playerManager.GetPlayerHealth(pid);
                    var hasInstance = _instanceManager.GetInstanceByPlayerId(pid) != null;
                    Log.Info($"[{_logName}] Player {pid}: Eliminated={isEliminated}, Health={health}, HasInstance={hasInstance}");
                }

                // 为每个玩家发送回合开始通知
                Log.Info($"[{_logName}] Sending RoundStart notifications to {playerIds.Count} active players...");
                foreach (var playerId in playerIds)
                {
                    // 检查玩家是否在战斗实例中
                    var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                    if (instance == null)
                    {
                        // 玩家没有战斗实例，可能是与AI对战，仍然需要发送通知
                        Log.Info($"[{_logName}] Player {playerId} not in battle instance (vs AI), sending RoundStart notification anyway");

                        // 为与AI对战的玩家发送简化的回合开始通知
                        SendRoundStartNotificationForAIBattle(playerId, hasBuffSelection);
                        continue;
                    }
                    // 获取该玩家的Buff选项
                    var buffers = new List<int>();
                    if (hasBuffSelection)
                    {
                        var playerBuffOptions = _buffManager.GetPlayerBuffOptions(playerId);
                        buffers.AddRange(playerBuffOptions.Select(b => b.BuffId));

                        // 添加调试日志
                        var buffIds = string.Join(", ", buffers);
                        Log.Info($"[{_logName}] RoundStart: Player {playerId} buff options: [{buffIds}]");
                    }
                    // RoundStart阶段按固定顺序发送双方数据：[第一个玩家, 第二个玩家]
                    var playerBoards = new List<PBPlayerBoard>();

                    // 按照战斗实例中的玩家顺序创建PBPlayerBoard（第一个玩家使用1-30，第二个玩家使用31-60）
                    foreach (var instancePlayerId in instance.PlayerIds)
                    {
                        var playerBoard = new PBPlayerBoard
                        {
                            Uid = (ulong)instancePlayerId
                        };

                        // 获取当前回合的棋盘数据
                        var entities = instance.CheckerBoard.GetPlayerEntities(instancePlayerId);
                        foreach (var entity in entities)
                        {
                            // 只包含在棋盘上的实体（排除临时位实体）
                            if (entity.EntityId != 0 && entity.GridX > 0 && entity.GridY > 0)
                            {
                                // 使用绝对GridID，不进行坐标转换
                                var gridId = (entity.GridX - 1) * 6 + entity.GridY;

                                // 从玩家阵容中获取英雄的局外养成信息（Level和AwakeLevel）
                                var lineupHeroInfo = _playerManager.GetLineupHeroInfo(instancePlayerId, entity.ConfigId);

                                playerBoard.BoardInfo.Add(new PBCheckerBoard
                                {
                                    GridID = gridId, // 使用绝对GridID
                                    Hero = new PBBattleHeroInfo
                                    {
                                        Id = entity.ConfigId,
                                        Level = lineupHeroInfo?.Level ?? 1,
                                        StarLevel = entity.StarLevel,
                                        AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0
                                    }
                                });
                            }
                        }

                        playerBoards.Add(playerBoard);
                        Log.Info($"[{_logName}] RoundStart board data: player:{instancePlayerId} heroes:{playerBoard.BoardInfo.Count}");
                    }

                    var req = new RoundStartReq
                    {
                        Uid = (ulong)playerId,
                    };
                    req.Buffers.AddRange(buffers);

                    // 使用更新后的协议：按玩家归类的棋盘数据
                    req.PlayerBoards.AddRange(playerBoards);

                    // 获取玩家的服务器ID
                    var playerServerId = _playerManager.GetPlayerServerId(playerId);

                    Log.Info($"[{_logName}] Sending RoundStart to Player {playerId} on GameServer {playerServerId}");
                    Log.Info($"[{_logName}] RoundStart message: Buffers={buffers.Count}, PlayerBoards={playerBoards.Count}");

                    NatsClient.GameServiceClient.RoundStart(req, playerServerId).ConfigureAwait(false);
                }

                Log.Info($"[{_logName}] Successfully sent RoundStart notifications to all {playerIds.Count} players via NATS");

                // 验证回合开始后所有实例的格子一致性
                foreach (var playerId in playerIds)
                {
                    var instance = _instanceManager.GetInstanceByPlayerId(playerId);
                    instance?.CheckerBoard.ValidateAndLogConsistency($"RoundStart(Round {_battleStateManager.RoundCount})");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] CRITICAL ERROR: Failed to send RoundStart notification to GameServer!");
                Log.Error($"[{_logName}] Exception: {ex.Message}");
                Log.Error($"[{_logName}] Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 通知GameServer战斗开始
        /// </summary>
        private void NotifyGameServerRoundBattleStart()
        {
            try
            {
                if (NatsClient.GameServiceClient == null)
                {
                    Log.Warning($"[{_logName}] NATS GameService client not available for RoundBattleStart");
                    return;
                }

                // 生成随机种子
                var seed = Random.Shared.Next();

                // 获取当前的战斗实例配对（使用已经生成的配对，不重新生成）
                var activePlayerIds = _playerManager.GetActivePlayerIds();
                var opponentPairs = new Dictionary<long, long>();

                // 从PlayerManager获取已设置的对手关系
                foreach (var playerId in activePlayerIds)
                {
                    var opponentId = _playerManager.GetPlayerOpponent(playerId);
                    if (opponentId != 0)
                    {
                        opponentPairs[playerId] = opponentId;
                    }
                }

                // 为每个玩家发送战斗开始通知
                foreach (var playerId in activePlayerIds)
                {
                    // 检查玩家是否在战斗实例中
                    var playerInstance = _instanceManager.GetInstanceByPlayerId(playerId);
                    if (playerInstance == null)
                    {
                        Log.Warning($"[{_logName}] Player {playerId} not in any battle instance, skipping RoundBattleStart notification");
                        continue;
                    }

                    if (opponentPairs.TryGetValue(playerId, out var opponentId))
                    {
                        // 创建战斗开始请求，确保GridID唯一性
                        var req = CreateRoundBattleStartRequest(playerId, opponentId, seed);

                        // 获取玩家的服务器ID
                        var playerServerId = _playerManager.GetPlayerServerId(playerId);
                        NatsClient.GameServiceClient.RoundBattleStart(req, playerServerId).ConfigureAwait(false);

                        Log.Info($"[{_logName}] Sent RoundBattleStart to Player {playerId} vs Opponent {opponentId} with {req.Team.Count} teams");
                    }
                }

                Log.Info($"[{_logName}] Sent RoundBattleStart notifications with seed: {seed}");
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Failed to send RoundBattleStart notification: {ex.Message}");
            }
        }

        /// <summary>
        /// 通知GameServer回合战斗结束
        /// </summary>
        private void NotifyGameServerRoundBattleEnd(long winnerId, long loserId)
        {
            try
            {
                if (NatsClient.GameServiceClient == null)
                {
                    Log.Warning($"[{_logName}] NATS GameService client not available for RoundBattleEnd");
                    return;
                }

                // 检查是否为最后回合（游戏即将结束）
                var activePlayerIds = _playerManager.GetActivePlayerIds();
                bool isGameEnding = activePlayerIds.Count <= 1;

                // 为相关玩家发送战斗结束通知
                var playersToNotify = new List<long>();
                if (winnerId > 0) playersToNotify.Add(winnerId);
                if (loserId > 0) playersToNotify.Add(loserId);

                foreach (var playerId in playersToNotify)
                {
                    var req = new RoundBattleEndReq
                    {
                        Uid = (ulong)playerId,
                        WinUid = (ulong)winnerId,
                        LoseUid = (ulong)loserId,
                        IsEnd = isGameEnding // 设置整场战斗是否即将结束
                    };

                    // 获取玩家的服务器ID
                    var playerServerId = _playerManager.GetPlayerServerId(playerId);
                    NatsClient.GameServiceClient.RoundBattleEnd(req, playerServerId).ConfigureAwait(false);
                }

                if (isGameEnding)
                {
                    Log.Info($"[{_logName}] Sent FINAL RoundBattleEnd notification (isEnd=true) - Winner: {winnerId}, Loser: {loserId}");
                }
                else
                {
                    Log.Info($"[{_logName}] Sent RoundBattleEnd notification (isEnd=false) - Winner: {winnerId}, Loser: {loserId}");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Failed to send RoundBattleEnd notification: {ex.Message}");
            }
        }

        /// <summary>
        /// 通知GameServer整场战斗结束
        /// </summary>
        private void NotifyGameServerBattleEnd()
        {
            try
            {
                if (NatsClient.GameServiceClient == null)
                {
                    Log.Warning($"[{_logName}] NATS GameService client not available for BattleEnd");
                    return;
                }

                // 获取所有玩家信息并计算排名
                var allPlayerIds = _playerManager.GetAllPlayerIds();
                var activePlayerIds = _playerManager.GetActivePlayerIds();
                var eliminatedPlayerIds = _playerManager.GetEliminatedPlayerIds();

                // 计算最终排名：存活玩家排名靠前，被淘汰玩家按淘汰顺序排名
                var finalRanking = new Dictionary<long, int>();
                int rank = 1;

                // 存活玩家按血量排序（血量高的排名靠前）
                var survivorsByHealth = activePlayerIds
                    .OrderByDescending(id => _playerManager.GetPlayerHealth(id))
                    .ToList();
                foreach (var playerId in survivorsByHealth)
                {
                    finalRanking[playerId] = rank++;
                }

                // 被淘汰玩家按淘汰顺序排名（后淘汰的排名靠前）
                foreach (var playerId in eliminatedPlayerIds.AsEnumerable().Reverse())
                {
                    finalRanking[playerId] = rank++;
                }

                // 为每个玩家发送战斗结束通知
                foreach (var playerId in allPlayerIds)
                {
                    // 获取玩家最终阵容
                    var finalHeroes = GetPlayerFinalLineup(playerId);

                    var req = new BattleEndReq
                    {
                        Uid = (ulong)playerId,
                        BattleId = BattleId,
                        Rank = finalRanking.GetValueOrDefault(playerId, 4), // 默认排名4
                        WinStreak = 0 // TODO: 需要从玩家数据中获取连胜数
                    };
                    req.Heros.AddRange(finalHeroes);

                    // 获取玩家的服务器ID
                    var playerServerId = _playerManager.GetPlayerServerId(playerId);
                    NatsClient.GameServiceClient.BattleEnd(req, playerServerId).ConfigureAwait(false);
                }

                Log.Info($"[{_logName}] Sent BattleEnd notifications for {allPlayerIds.Count} players");
            }
            catch (Exception ex)
            {
                Log.Error($"[{_logName}] Failed to send BattleEnd notification: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建回合战斗开始请求（按战斗实例PlayerIds顺序，与RoundStart保持一致）
        /// </summary>
        /// <param name="playerId">接收请求的玩家ID</param>
        /// <param name="opponentId">对手玩家ID</param>
        /// <param name="seed">随机种子</param>
        /// <returns>战斗开始请求</returns>
        private RoundBattleStartReq CreateRoundBattleStartRequest(long playerId, long opponentId, int seed)
        {
            var req = new RoundBattleStartReq
            {
                Uid = (ulong)playerId,
                BattleId = BattleId,
                Seed = seed
            };

            // 获取玩家所在的战斗实例
            var instance = _instanceManager.GetInstanceByPlayerId(playerId);
            if (instance == null)
            {
                Log.Warning($"[{_logName}] No battle instance found for player {playerId} in CreateRoundBattleStartRequest");
                return req;
            }

            // 全局GridID去重集合，确保同一请求中的GridID唯一
            var globalUsedGridIds = new HashSet<int>();

            // 按照战斗实例中的PlayerIds顺序创建Team数组（与RoundStart保持一致）
            foreach (var instancePlayerId in instance.PlayerIds)
            {
                var campInfo = CreateBattleCampInfoWithGlobalDedup(instancePlayerId, playerId, globalUsedGridIds);
                req.Team.Add(campInfo);
            }

            Log.Info($"[{_logName}] Created RoundBattleStart request for player {playerId}, Team order: [{string.Join(", ", instance.PlayerIds)}], total GridIDs used: {globalUsedGridIds.Count}");
            return req;
        }

        /// <summary>
        /// 创建战斗阵营信息（使用绝对GridID）
        /// </summary>
        /// <param name="playerId">要创建阵营信息的玩家ID</param>
        /// <param name="receiverId">接收此信息的玩家ID（已废弃，保留兼容性）</param>
        /// <param name="globalUsedGridIds">全局已使用的GridID集合</param>
        /// <returns>阵营信息</returns>
        private PBBattleCampInfo CreateBattleCampInfoWithGlobalDedup(long playerId, long receiverId, HashSet<int> globalUsedGridIds)
        {
            var campInfo = new PBBattleCampInfo();

            // 设置玩家信息
            campInfo.Player = new PBBattlePlayerInfo
            {
                Uid = (ulong)playerId,
                Name = _playerManager.GetPlayerName(playerId),
                Level = _playerManager.GetPlayerLevel(playerId),
                Throphy = _playerManager.GetPlayerTrophy(playerId),
                ServerId = _gameServerId,
                Hp = _playerManager.GetPlayerHealth(playerId)
            };

            // 获取玩家所在的战斗实例
            var instance = _instanceManager.GetInstanceByPlayerId(playerId);
            if (instance != null)
            {
                var entities = instance.CheckerBoard.GetPlayerEntities(playerId);
                var processedEntityIds = new HashSet<int>(); // 本次调用的实体ID去重

                // 移除冗余的调试日志

                foreach (var entity in entities)
                {
                    // 只处理棋盘上的有效实体
                    if (entity.EntityId == 0 || entity.GridX <= 0 || entity.GridY <= 0)
                        continue;

                    // 检查实体ID是否已处理（静默跳过重复）
                    if (processedEntityIds.Contains(entity.EntityId))
                        continue;
                    processedEntityIds.Add(entity.EntityId);

                    // 使用绝对GridID，不进行坐标转换
                    var gridId = (entity.GridX - 1) * 6 + entity.GridY;
                    if (gridId < 1 || gridId > 60)
                    {
                        Log.Warning($"[{_logName}] Invalid GridID {gridId} for entity {entity.EntityId}");
                        continue;
                    }

                    // 全局去重检查
                    if (globalUsedGridIds.Contains(gridId))
                    {
                        Log.Error($"[{_logName}] CRITICAL: GridID {gridId} already used in this request! Entity {entity.EntityId} at ({entity.GridX},{entity.GridY}) for player {playerId}");
                        continue; // 跳过重复的GridID
                    }
                    globalUsedGridIds.Add(gridId);

                    // 获取英雄信息并添加到阵营
                    var lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, entity.ConfigId);
                    campInfo.BoardInfo.Add(new PBCheckerBoard
                    {
                        GridID = gridId, // 使用绝对GridID
                        Hero = new PBBattleHeroInfo
                        {
                            Id = entity.ConfigId,
                            Level = lineupHeroInfo?.Level ?? 1,
                            StarLevel = entity.StarLevel,
                            AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0
                        }
                    });

                    // 移除冗余的调试日志
                }

                Log.Info($"[{_logName}] Camp info for player {playerId}: {campInfo.BoardInfo.Count} heroes added");
            }
            else
            {
                Log.Warning($"[{_logName}] No battle instance found for player {playerId}");
            }

            return campInfo;
        }

        /// <summary>
        /// 创建战斗阵营信息（原有方法，保持兼容性）
        /// </summary>
        /// <param name="playerId">要创建阵营信息的玩家ID</param>
        /// <param name="receiverId">接收此信息的玩家ID（已废弃，保留兼容性）</param>
        private PBBattleCampInfo CreateBattleCampInfo(long playerId, long receiverId)
        {
            // 使用独立的去重集合调用新的方法
            var localUsedGridIds = new HashSet<int>();
            return CreateBattleCampInfoWithGlobalDedup(playerId, receiverId, localUsedGridIds);
        }





        /// <summary>
        /// 格子ID转坐标 (ID从1开始)
        /// 编号规则：从下往上，从左往右递增
        /// </summary>
        private (int row, int col) GridIdToCoord(int gridId)
        {
            gridId--; // 转为0基索引
            int row = gridId / 6 + 1;
            int col = gridId % 6 + 1;
            return (row, col);
        }

        /// <summary>
        /// 坐标转格子ID
        /// 编号规则：从下往上，从左往右递增
        /// </summary>
        private int CoordToGridId(int row, int col)
        {
            return (row - 1) * 6 + (col - 1) + 1;
        }

        /// <summary>
        /// 确定玩家应该使用的固定GridID区域
        /// 根据玩家在战斗实例PlayerIds中的位置来分配区域，确保推送顺序的一致性
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>true表示使用1-30区域，false表示使用31-60区域</returns>
        private bool ShouldUseMyArea(long playerId)
        {
            // 获取玩家所在的战斗实例
            var instance = _instanceManager.GetInstanceByPlayerId(playerId);
            if (instance == null)
            {
                Log.Warning($"[{_logName}] No instance found for player {playerId} in ShouldUseMyArea, defaulting to MyArea");
                return true; // 默认使用1-30区域
            }

            // 根据玩家在实例PlayerIds中的位置分配区域
            // 第一个玩家（下标0）使用1-30区域，第二个玩家（下标1）使用31-60区域
            int playerIndex = instance.PlayerIds.IndexOf(playerId);
            if (playerIndex == -1)
            {
                Log.Warning($"[{_logName}] Player {playerId} not found in instance PlayerIds, defaulting to MyArea");
                return true; // 默认使用1-30区域
            }

            // 第一个玩家（下标0）使用MyArea（1-30），第二个玩家（下标1）使用EnemyArea（31-60）
            return playerIndex == 0;
        }




    }
}
